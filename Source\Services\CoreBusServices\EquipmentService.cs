using Camstar.XMLClient.API;
using DataEntities.Models;
using Entities.Models;
using Entities.Models.Eap;
using Entities.Models.EQP;
using Infrastructure.CamStarProvider;
using Services.Common;
using Services.CoreBusRepository;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Entities.Enums;
using AutoMapper;
using OutsideService.EAM.Models;
using OutsideService.EAM;
using Entities.Models.Eam;
using System.Collections.Concurrent;
using OutsideService.Comm;
using Entities.Models.Common;
using Newtonsoft.Json;
using Platform.Common;
using Services.Models;
using Infrastructure.Configurable;
using services.BusinessRepository;
using System.Threading;
using System.Configuration;
using Infrastructure.Utilities;
using System.Web.UI.WebControls;
using System.Reflection;
using System.Data.Entity.Infrastructure;
using SqlSugar;
using Entities.Models.MES;
using System.Collections;
using DataAccessor.RedisProvider;

//using PlantWebAPI.Extensions;

namespace Services.CoreBusServices
{
    /// <summary>
    /// 表示设备时间点数据
    /// </summary>
    public class TimePointData
    {
        /// <summary>
        /// 产量
        /// </summary>
        public int OutputQty { get; set; }

        /// <summary>
        /// 小时产量
        /// </summary>
        public int HourlyOutput { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 将TimePointData转换为字典
        /// </summary>
        public Dictionary<string, string> ToDictionary()
        {
            return new Dictionary<string, string>
            {
                { "outputQty", OutputQty.ToString() },
                { "hourlyOutput", HourlyOutput.ToString() },
                { "updateTime", UpdateTime.ToString("yyyy-MM-dd HH:mm:ss") }
            };
        }

        /// <summary>
        /// 从字典创建TimePointData
        /// </summary>
        public static TimePointData FromDictionary(Dictionary<string, string> dict)
        {
            var result = new TimePointData();

            if (dict != null)
            {
                if (dict.ContainsKey("outputQty"))
                {
                    int.TryParse(dict["outputQty"], out int outputQty);
                    result.OutputQty = outputQty;
                }

                if (dict.ContainsKey("hourlyOutput"))
                {
                    int.TryParse(dict["hourlyOutput"], out int hourlyOutput);
                    result.HourlyOutput = hourlyOutput;
                }

                if (dict.ContainsKey("updateTime"))
                {
                    DateTime.TryParse(dict["updateTime"], out DateTime updateTime);
                    result.UpdateTime = updateTime;
                }
            }

            return result;
        }
    }

    public class EquipmentService : BaseService
    {
        private string ParasChangeEmailGroup = ConfigReader.ConfigEntity.SendEmailSetting?.ParasChangeEmailGroup;
        private readonly IDateTimeProvider _dateTimeProvider;

        // 默认构造函数 - 保持向后兼容性
        public EquipmentService()
            : this(new DatabaseDateTimeProvider())
        {
        }

        // 带时间提供者的构造函数 - 用于测试
        public EquipmentService(IDateTimeProvider dateTimeProvider)
        {
            _dateTimeProvider = dateTimeProvider;
        }

        // 设备状态调整
        public ResultStatus EquipmentSetStatus(EQPSetStatusEntity eQPSetStatusEntity)
        {
            //   <Comments><![CDATA[123123]]></Comments>
            ResultStatus resultStatus = new ResultStatus();
            NXmlClient nXmlClient = new NXmlClient();
            resultStatus = nXmlClient.NormalSubmit("EquipmentSetStatus", eQPSetStatusEntity.User, eQPSetStatusEntity.PassWord, inputData =>
            {

                inputData.namedObjectField("Resource").setRef(eQPSetStatusEntity.ResourceName);
                inputData.namedObjectField("ResourceStatusCode").setRef(eQPSetStatusEntity.StatusCodeName);
                inputData.namedObjectField("ResourceStatusReason").setRef(eQPSetStatusEntity.StatusReasonName);
                inputData.dataField("Comments").setValue(eQPSetStatusEntity.Comments);
                inputData.dataField("ComputerName").setValue(eQPSetStatusEntity.User);
            });

            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"设备状态设置");
            return resultStatus;
        }

        /// <summary>
        /// 设备报修
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public ResultStatus EquipmentReportRepair(EQPReportRepairEntity entity)
        {
            //   <Comments><![CDATA[123123]]></Comments>
            ResultStatus resultStatus = new ResultStatus();
            NXmlClient nXmlClient = new NXmlClient();
            nXmlClient.InitializeSession(entity.User, entity.PassWord);
            resultStatus = nXmlClient.NormalSubmit("JobCreate", inputData =>
            {
                inputData.dataField("ComputerName").setValue(entity.User);
                inputData.dataField("IsSimpleMode").setValue("0");
                inputData.namedObjectField("JobModel").setRef("维修模式");//("Repair Sample");//
                inputData.namedObjectField("Resource").setRef(entity.Equipment);
                inputData.namedObjectField("Stage").setRef("开始");//("Start");//
                inputData.dataField("StageSequence").setValue("1");
                inputData.dataField("jaisShutDown").setValue(entity.ShutDown);
                inputData.dataField("jaFaultDescription").setValue(entity.FaultDescription);
                if (entity.RepairManList.Count > 0)
                {
                    var TechniciansData = inputData.subentityList("Technicians");
                    for (int i = 0; i < entity.RepairManList.Count; i++)
                    {
                        var item = TechniciansData.appendItem();

                        item.namedObjectField("Technician").setRef(entity.RepairManList[i].value.Split('/')[0]);
                    }
                }
            });

            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"设备报修提交");
            return resultStatus;
        }

        /// <summary>
        /// 设备维修人员分配（PDA）
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public ResultStatus EquipmentAssignRepairMan(EQPStartRepairEntity entity)
        {
            string msg = "";
            ResultStatus resultStatus = new ResultStatus();
            NXmlClient nXmlClient = new NXmlClient();
            EquipmentRepository er = new EquipmentRepository();
            nXmlClient.InitializeSession(entity.User, entity.PassWord);
            //nXmlClient.InitializeSession("CamstarAdmin", er.GetPasswordByUsername("CamstarAdmin"));
            resultStatus = nXmlClient.NormalSubmit("JobAssign", inputData =>
            {
                inputData.dataField("ComputerName").setValue(entity.User);
                inputData.dataField("IsSimpleMode").setValue(entity.IsSimpleMode);
                inputData.namedObjectField("JobModel").setRef(entity.JobModel);
                inputData.namedObjectField("JobOrder").setRef(entity.JobOrder);
                inputData.namedObjectField("Resource").setRef(entity.Equipment);
                inputData.namedObjectField("Stage").setRef(entity.Stage);
                inputData.dataField("StageSequence").setValue(entity.StageSequence);
                if (entity.RepairMan.Count > 0)
                {
                    var TechniciansData = inputData.subentityList("Technicians");
                    for (int i = 0; i < entity.RepairMan.Count; i++)
                    {
                        var item = TechniciansData.appendItem();

                        item.namedObjectField("Technician").setRef(entity.RepairMan[i].value.Split('/')[0]);
                    }
                }
            });
            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"设备维修人员分配") + msg;
            return resultStatus;
        }

        /// <summary>
        /// 设备维修开始确认（PDA）
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public ResultStatus EquipmentStartRepair(EQPStartRepairEntity entity)
        {
            string msg = "";
            ResultStatus resultStatus = new ResultStatus();
            NXmlClient nXmlClient = new NXmlClient();
            EquipmentRepository er = new EquipmentRepository();
            nXmlClient.InitializeSession(entity.User, entity.PassWord);
            //nXmlClient.InitializeSession("CamstarAdmin", er.GetPasswordByUsername("CamstarAdmin"));
            resultStatus = nXmlClient.NormalSubmit("JobAcknowledge", inputData =>
            {
                inputData.dataField("AutoClockOn").setValue("1");
                inputData.dataField("ComputerName").setValue(entity.User);
                inputData.dataField("ExpectedStartDate").setFormattedValue(DateTime.Parse(entity.ExpectedStartDate).ToString("yyyy-MM-dd HH:mm:ss"), dataFormats.formatDate);
                inputData.dataField("IsSimpleMode").setValue(entity.IsSimpleMode);
                inputData.namedObjectField("JobModel").setRef(entity.JobModel);
                inputData.namedObjectField("JobOrder").setRef(entity.JobOrder);
                inputData.namedObjectField("Resource").setRef(entity.Equipment);
                inputData.namedObjectField("Stage").setRef(entity.Stage);//("Start");//
                inputData.dataField("StageSequence").setValue(entity.StageSequence);
            });
            if (resultStatus.ResultCode == "1")
            {
                if (entity.ShutDown == "1")
                {
                    //1、调用 修改设备状态的API
                    EQPSetStatusEntity ese = new EQPSetStatusEntity();
                    ese.ResourceName = entity.Equipment;
                    ese.StatusCodeName = "故障停机";
                    ese.Comments = "设备停机维修";
                    ese.User = entity.User;
                    ese.PassWord = entity.PassWord;
                    ResultStatus rs = EquipmentSetStatus(ese);
                    if (rs.ResultCode == "0")
                    {
                        msg += "," + rs.ResultMsg;
                    }
                }
                //2、调用修改维修单 isShutDown
                AJobEntity ajob = new AJobEntity();
                ajob.JobID = entity.JOBID;
                ajob.JaIsShutDown = entity.ShutDown;
                ResultStatus rs0 = UpdateAJob(ajob);
                if (rs0.ResultCode == "0")
                {
                    msg += ",修改报修单中是否停机失败";
                }
                //int update = er.UpdateRepairShutDownById(entity.JOBID, entity.ShutDown);
                //if (update == -1)
                //{
                //    msg += ",修改报修单中是否停机失败";
                //}
            }
            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"设备维修开始确认") + msg;
            return resultStatus;
        }

        /// <summary>
        /// 根据jobid修改A_Job
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public ResultStatus UpdateAJob(AJobEntity entity)
        {
            ResultStatus resultStatus = new ResultStatus();
            NXmlClient nXmlClient = new NXmlClient();
            //nXmlClient.InitializeSession(entity.User, entity.PassWord);
            resultStatus = nXmlClient.NormalSubmit("jaUpdateAJob", inputData =>
            {
                inputData.dataField("JobID").setValue(entity.JobID);
                inputData.dataField("jaisshutdown").setValue(entity.JaIsShutDown);
                inputData.dataField("jaProcessCondition").setValue(entity.JaProcessCondition);
                inputData.dataField("jaIsRepairComplete").setValue(entity.JaIsRepairComplete);
                inputData.dataField("jaProcessVerifier").setValue(entity.JaProcessVerifier);
            });
            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"设备维修完成确认");
            return resultStatus;
        }

        /// <summary>
        /// 设备维修完成报修人确认（PDA）
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public ResultStatus EquipmentRepairCompleteConfirm(EQPStartRepairEntity entity)
        {
            ResultStatus resultStatus = new ResultStatus();
            NXmlClient nXmlClient = new NXmlClient();
            nXmlClient.InitializeSession(entity.User, entity.PassWord);
            resultStatus = nXmlClient.NormalSubmit("JobComplete", inputData =>
            {
                inputData.dataField("AutoClockOn").setValue("0");
                inputData.dataField("AutoCompleteMaintenance").setValue("0");
                inputData.dataField("ComputerName").setValue(entity.User);
                inputData.dataField("IsSimpleMode").setValue(entity.IsSimpleMode);
                inputData.namedObjectField("JobModel").setRef(entity.JobModel);
                inputData.namedObjectField("JobOrder").setRef(entity.JobOrder);
                inputData.namedObjectField("Resource").setRef(entity.Equipment);
                inputData.namedObjectField("Stage").setRef(entity.Stage);//("Start");//
                inputData.dataField("StageSequence").setValue(entity.StageSequence);
                inputData.dataField("jaisRepairComplete").setValue(entity.IsRepairComplete);
            });

            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"设备维修完成报修人确认");
            return resultStatus;
        }

        public ResultStatus DefectUpdateService(DefectUpdateEntity entity)
        {
            ResultStatus resultStatus = new ResultStatus();
            NXmlClient nXmlClient = new NXmlClient();
            resultStatus = nXmlClient.NormalSubmit("jaUpdateDefectOrder", inputData =>
            {
                inputData.dataField("jaDefectNo").setValue(entity.jaDefectNo);
                inputData.dataField("jaStatus").setValue(entity.jaStatus);
                inputData.dataField("jaRepairOrder").setValue(entity.jaRepairOrder);
            });

            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"设备缺陷修改");
            return resultStatus;
        }

        /// <summary>
        /// 设备维修
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public ResultStatus EquipmentRepair(EQPRepairEntity entity)
        {
            ResultStatus resultStatus = new ResultStatus();
            EquipmentRepository err = new EquipmentRepository();
            // 检查涉酸记录人员
            if (entity.AcidList != null && entity.AcidList.Count > 0)
            {
                List<string> userList = entity.AcidList.Select(o => o.AcidInvolvedEmployee).ToList();
                Dictionary<string, string> dic = err.GetUserID(userList);
                for (int i = 0; i < entity.AcidList.Count; i++)
                {
                    if (dic.ContainsKey(entity.AcidList[i].AcidInvolvedEmployee))
                    {
                        entity.AcidList[i].AcidInvolvedEmployeeID = dic[entity.AcidList[i].AcidInvolvedEmployee];
                    }
                    else
                    {
                        resultStatus.ResultCode = "0";
                        resultStatus.ResultMsg = $"员工工号不存在：{entity.AcidList[i].AcidInvolvedEmployee}";
                        return resultStatus;
                    }
                }
            }

            NXmlClient nXmlClient = new NXmlClient();
            nXmlClient.InitializeSession(entity.User, entity.PassWord);
            resultStatus = nXmlClient.NormalSubmit("JobProgress", inputData =>
            {
                inputData.dataField("AutoClockOff").setValue("1");
                inputData.dataField("AutoCompleteMaintenance").setValue("0");
                inputData.dataField("ComputerName").setValue(entity.User);
                inputData.dataField("IsSimpleMode").setValue(entity.IsSimpleMode);
                inputData.namedObjectField("JobModel").setRef(entity.JobModel);
                inputData.namedObjectField("JobOrder").setRef(entity.JobOrder);
                inputData.namedObjectField("Resource").setRef(entity.Resource);
                inputData.namedObjectField("Stage").setRef(entity.Stage);
                inputData.dataField("StageSequence").setValue(entity.StageSequence);
                inputData.namedObjectField("ToStage").setRef("维修");
                inputData.dataField("ToStageSequence").setValue("2");
                inputData.dataField("jaRepairUseMinute").setValue(entity.RepairUseMinute);

                //csiNamedObjectList carrierlist = inputData.namedObjectList("Checklist");
                //for (int i = 0; i < 1; i++)
                //{
                //    var item = carrierlist.appendItem("");

                //}
                //var TechniciansData = inputData.subentityList("Checklist");
                //var item = TechniciansData.appendItem();
                //item.dataField("ChecklistId").setValue("AA");
                //item.dataField("Instruction").setValue("AB");

                inputData.dataField("jaisTransferProcess").setValue(entity.IsTransferProcess);
                inputData.dataField("jaFaultDescription").setValue(entity.FaultDescription);
                inputData.dataField("jaRepairFunction").setValue(entity.RepairFunction);
                inputData.dataField("jaFaultCause").setValue(entity.FaultCause);
                inputData.dataField("jaCorrectiveActions").setValue(entity.CorrectiveActions);
                inputData.dataField("jaRemainingProblems").setValue(entity.RemainingProblems);

                inputData.dataField("jaResourceFaultNature").setValue(entity.ResourceFaultNature);
                inputData.dataField("jaResourceFaultMode").setValue(entity.ResourceFaultMode);
                inputData.dataField("jaResourceFaultSpecialty").setValue(entity.ResourceFaultSpecialty);
            });

            if (resultStatus.ResultCode == "1")
            {
                if (entity.AcidList != null && entity.AcidList.Count > 0)
                {
                    // 涉酸记录
                    EquipmentAcidInvolved(entity.AcidList, err.GetUserID(new List<string>() { entity.User })[entity.User], "维修");
                }
                if (entity.MoveOrderDetailList != null && entity.MoveOrderDetailList.Count > 0)
                {
                    //备件使用记录
                    EquipmentMoveOrderUse(entity.MoveOrderDetailList, entity.User, "维修");
                }
            }

            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"设备维修提交");
            return resultStatus;
        }

        // 设备保养执行
        public ResultStatus EquipmentRequirement(EQPRequirementEntity eQPRequirementEntity)
        {
            ResultStatus resultStatus = new ResultStatus();
            // 检查涉酸记录人员
            if (eQPRequirementEntity.AcidList != null && eQPRequirementEntity.AcidList.Count > 0)
            {
                XmlClient oClient = new XmlClient();
                //
                for (int i = 0; i < eQPRequirementEntity.AcidList.Count; i++)
                {
                    // 查询员工
                    string sql = "select employeeid,fullname from employee where employeename = '{0}'";
                    sql = string.Format(sql, eQPRequirementEntity.AcidList[i].AcidInvolvedEmployee);
                    DataTable dt = oClient.AdhocQuery(sql);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        eQPRequirementEntity.AcidList[i].AcidInvolvedEmployeeID = dt.Rows[0]["employeeid"].ToString();
                    }
                    else
                    {
                        resultStatus.ResultCode = "0";
                        resultStatus.ResultMsg = $"员工工号不存在：{eQPRequirementEntity.AcidList[i].AcidInvolvedEmployee}";
                        return resultStatus;
                    }
                }

                // eQPRequirementEntity.UserID 得用户ID不可靠，暂时先查询
                string sql2 = "select employeeid,fullname from employee where employeename = '{0}'";
                sql2 = string.Format(sql2, eQPRequirementEntity.UserName);
                DataTable dt2 = oClient.AdhocQuery(sql2);
                if (dt2 != null && dt2.Rows.Count > 0)
                {
                    eQPRequirementEntity.UserID = dt2.Rows[0]["employeeid"].ToString();
                }
            }

            string curTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            NXmlClient nXmlClient = new NXmlClient(); // jaCompleteMaintenance CompleteMaintenance
            resultStatus = nXmlClient.NormalSubmit("jaCompleteMaintenance", eQPRequirementEntity.UserName, eQPRequirementEntity.PassWord, inputData =>
            {
                if (eQPRequirementEntity.DataCollectionDefName != "")
                {
                    inputData.revisionedObjectField("DataCollectionDef").setRef(eQPRequirementEntity.DataCollectionDefName, "1", false);
                }
                inputData.dataField("ForceMaintenance").setValue("false");
                inputData.revisionedObjectField("MaintenanceReq").setRef(eQPRequirementEntity.MaintenanceReqName, "", true);

                if (eQPRequirementEntity.DataPointList != null && eQPRequirementEntity.DataPointList.Count > 0)
                {
                    var csiParametricData = inputData.namedObjectField("ParametricData");
                    csiParametricData.setAttribute("__action", "create");
                    csiParametricData.setObjectType("DataPointSummary");

                    var csiDataPointList = csiParametricData.subentityList("DataPointDetails");

                    foreach (var item in eQPRequirementEntity.DataPointList)
                    {
                        var csiSub = csiDataPointList.appendItem();
                        csiSub.setAttribute("__listItemAction", "add");
                        csiSub.setObjectType("DataPointDetails");

                        var csiNSub = csiSub.namedSubentityField("DataPoint");

                        csiNSub.setName(item.DataPointName);
                        csiNSub.parentInfo().setObjectId(item.DataCollectionDefID);
                        csiNSub.parentInfo().setRevisionedObjectRef(item.DataCollectionDefName, "1", false);

                        csiSub.dataField("DataType").setValue(item.DataType);
                        csiSub.dataField("DataValue").setValue(item.DataValue);
                    }


                    csiParametricData.dataField("OverrideDataPointLimits").setValue("True");
                }

                inputData.namedObjectField("Resource").setRef(eQPRequirementEntity.ResourceName);

                var csiServiceDetailsList = inputData.subentityList("ServiceDetails");
                var csiServiceDetail = csiServiceDetailsList.appendItem();

                var csiChecklist = csiServiceDetail.subentityList("Checklist");
                foreach (var item in eQPRequirementEntity.CheckListList)
                {
                    var fSub = csiChecklist.appendItem();
                    fSub.setAttribute("__listItemAction", "add");

                    fSub.dataField("ChecklistId").setValue(item.ChecklistID);
                    fSub.dataField("Comments").setValue(item.Comments);
                    fSub.dataField("Instruction").setValue(item.Instruction);
                    fSub.dataField("OverDueReason").setValue(eQPRequirementEntity.OverDueReason);
                }

                csiServiceDetail.namedObjectField("MaintenanceStatus").setObjectId(eQPRequirementEntity.MaintenanceStatusID);

                // 涉酸记录
                if (eQPRequirementEntity.AcidList != null && eQPRequirementEntity.AcidList.Count > 0)
                {
                    var csiAcidList = inputData.subentityList("EmployeeAcidInvolvedLis");

                    foreach (var item in eQPRequirementEntity.AcidList)
                    {
                        var csiAcidItem = csiAcidList.appendItem();
                        //csiAcidItem.setObjectType("EmployeeAcidInvolvedHis");
                        // var csiSub =   csiAcidItem.subentityField("EmployeeAcidInvolvedHis");
                        csiAcidItem.dataField("AcidInvolvedEmployeeID").setValue(item.AcidInvolvedEmployeeID);
                        csiAcidItem.dataField("CreatEmployeeID").setValue(eQPRequirementEntity.UserID);
                        csiAcidItem.dataField("CreatTime").setFormattedValue(curTime, dataFormats.formatDate);
                        csiAcidItem.dataField("DefectFromID").setValue(item.DefectFromID);
                        csiAcidItem.dataField("Duration").setValue(item.Duration);
                        csiAcidItem.dataField("ResourceID").setValue(item.ResourceID);
                        csiAcidItem.dataField("SourceType").setValue("保养");
                    }
                }

                // 备件使用
                if (eQPRequirementEntity.MoveOrderDetailList != null && eQPRequirementEntity.MoveOrderDetailList.Count > 0)
                {
                    var csiAcidList = inputData.subentityList("ResourceSparePartsUseDetailLis");

                    foreach (var item in eQPRequirementEntity.MoveOrderDetailList)
                    {
                        var csiAcidItem = csiAcidList.appendItem();
                        //csiAcidItem.setObjectType("ResourceSparePartsUseDetailHis");
                        // var csiSub =   csiAcidItem.subentityField("ResourceSparePartsUseDetailHis");
                        csiAcidItem.dataField("CreateTime").setFormattedValue(curTime, dataFormats.formatDate);
                        csiAcidItem.dataField("CreateUser").setValue(eQPRequirementEntity.UserName);
                        csiAcidItem.dataField("DefectFromID").setValue(item.DefectFromID);
                        csiAcidItem.dataField("MaterialNo").setValue(item.MaterialNo);
                        csiAcidItem.dataField("MoveOrderDetailID").setValue(item.MoveOrderDetailID);
                        csiAcidItem.dataField("MoveOrderInfoName").setValue(item.MoveOrderInfoName);
                        csiAcidItem.dataField("ResourceID").setValue(item.ResourceID);
                        csiAcidItem.dataField("UseQty").setValue(item.UseQty.ToString());
                        csiAcidItem.dataField("SourceType").setValue("保养");
                        csiAcidItem.dataField("TotalBackQty").setValue("0");
                    }
                }

                // 生成缺陷项
                if (eQPRequirementEntity.DataPointList != null && eQPRequirementEntity.DataPointList.Count > 0)
                {
                    List<DataPoint> defectList = new List<DataPoint>();
                    foreach (var item in eQPRequirementEntity.DataPointList)
                    {
                        // 检查是否合格
                        if (item.DataValue != "")
                        {
                            // 数据类型 1-Integer；2-Float；3-Fixed；4-String；5-Object；6-TimeStamp；7-Boolean；9-Decimal
                            if (item.DataType == "1" || item.DataType == "2" || item.DataType == "3" || item.DataType == "9")
                            {
                                // 数字值
                                if (ConvertHelper.ToDecimal(item.DataValue) < ConvertHelper.ToDecimal(item.Lowerlimit) || ConvertHelper.ToDecimal(item.DataValue) > ConvertHelper.ToDecimal(item.Upperlimit))
                                {
                                    defectList.Add(item);
                                }
                            }
                            else if (item.DataType == "7")
                            {
                                if (item.DataValue.ToUpper() == "FALSE")
                                {
                                    defectList.Add(item);
                                }
                            }
                        }
                    }
                    if (defectList != null && defectList.Count > 0)
                    {
                        int defectNo = GetDefectNo();
                        string defectNoDate = DateTime.Now.ToString("yyyyMMdd");
                        var csiAcidList = inputData.subentityList("jaEquipmentDefectManagementLis");
                        foreach (var item in defectList)
                        {
                            var csiAcidItem = csiAcidList.appendItem();
                            //csiAcidItem.setObjectType("EmployeeAcidInvolvedHis");
                            // var csiSub =   csiAcidItem.subentityField("EmployeeAcidInvolvedHis");
                            // csiAcidItem.dataField("AssignedMaintReqID").setValue(eQPRequirementEntity.AssignedMaintReqID);
                            csiAcidItem.dataField("CreateUser").setValue(eQPRequirementEntity.UserName);
                            csiAcidItem.dataField("CreateTime").setFormattedValue(curTime, dataFormats.formatDate);
                            csiAcidItem.dataField("DefectFromID").setValue(eQPRequirementEntity.MaintenanceReqID);

                            csiAcidItem.dataField("DataType").setValue(item.DataType);
                            csiAcidItem.dataField("DataValue").setValue(item.DataValue);
                            csiAcidItem.dataField("DefectItem").setValue(item.DataPointName);
                            csiAcidItem.dataField("DefectNo").setValue($"{defectNoDate}{(defectNo++).ToString().PadLeft(4, '0')}");
                            csiAcidItem.dataField("Lowerlimit").setValue(item.Lowerlimit);
                            // csiAcidItem.dataField("PlannedCompletionTime").setValue("2021-06-30 09:00:00");
                            csiAcidItem.dataField("ResourceID").setValue(item.ResourceID);
                            // csiAcidItem.dataField("RepairOrder").setValue("20");
                            csiAcidItem.dataField("Status").setValue("SUBMIT"); //COMPLETED
                            csiAcidItem.dataField("Upperlimit").setValue(item.Upperlimit);
                            csiAcidItem.dataField("SourceType").setValue("保养");
                        }
                    }
                }
            });
            #region -- 服务合并
            /*
            if (resultStatus.ResultCode == "1")
            {
                if (eQPRequirementEntity.AcidList != null && eQPRequirementEntity.AcidList.Count > 0)
                {
                    // 涉酸记录
                    EquipmentAcidInvolved(eQPRequirementEntity.AcidList, eQPRequirementEntity.UserID, "保养");
                }
                // 备件
                if (eQPRequirementEntity.MoveOrderDetailList != null && eQPRequirementEntity.MoveOrderDetailList.Count > 0)
                {
                    EquipmentMoveOrderUse(eQPRequirementEntity.MoveOrderDetailList, eQPRequirementEntity.UserName, "保养");
                }

                // 生成缺陷项
                if (eQPRequirementEntity.DataPointList != null && eQPRequirementEntity.DataPointList.Count > 0)
                {
                    List<DataPoint> defectList = new List<DataPoint>();
                    foreach (var item in eQPRequirementEntity.DataPointList)
                    {
                        // 检查是否合格
                        if (item.DataValue != "")
                        {
                            // 数据类型 1-Integer；2-Float；3-Fixed；4-String；5-Object；6-TimeStamp；7-Boolean；9-Decimal
                            if (item.DataType == "1" || item.DataType == "2" || item.DataType == "3" || item.DataType == "9")
                            {
                                // 数字值
                                if (ConvertHelper.ToDecimal(item.DataValue) < ConvertHelper.ToDecimal(item.Lowerlimit) || ConvertHelper.ToDecimal(item.DataValue) > ConvertHelper.ToDecimal(item.Upperlimit))
                                {
                                    defectList.Add(item);
                                }
                            }
                            else if (item.DataType == "7")
                            {
                                if (item.DataValue.ToUpper() == "FALSE")
                                {
                                    defectList.Add(item);
                                }
                            }
                        }
                    }
                    if (defectList != null && defectList.Count > 0)
                    {
                        EquipmentDefect(defectList, eQPRequirementEntity.UserName, eQPRequirementEntity.AssignedMaintReqID);
                    }
                }

            }
            */
            #endregion
            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"设备保养执行");
            return resultStatus;
        }

        public ResultStatus EquipmentRequirementTest()
        {
            ResultStatus resultStatus = new ResultStatus();
            NXmlClient nXmlClient = new NXmlClient();
            resultStatus = nXmlClient.NormalSubmit("CompleteMaintenance", inputData =>
            {
                inputData.revisionedObjectField("DataCollectionDef").setRef("传送带", "1", false);
                inputData.dataField("ForceMaintenance").setValue("false");
                inputData.revisionedObjectField("MaintenanceReq").setRef("日日保养", "", true);

                var csiSub1 = inputData.namedObjectField("ParametricData");
                csiSub1.setAttribute("__action", "create");
                csiSub1.setObjectType("DataPointSummary");

                var csiList = csiSub1.subentityList("DataPointDetails");

                // var csiList = inputData.subentityList("DataPointDetails");
                var csiSub = csiList.appendItem();
                csiSub.setAttribute("__listItemAction", "add");
                csiSub.setObjectType("DataPointDetails");

                var csiNSub = csiSub.namedSubentityField("DataPoint");
                csiNSub.setName("传送带");
                csiNSub.parentInfo().setObjectId("001c61800000007f");
                csiNSub.parentInfo().setRevisionedObjectRef("传送带", "1", false);
                csiSub.dataField("DataType").setValue("7");
                csiSub.dataField("DataValue").setValue("True");

                csiSub1.dataField("OverrideDataPointLimits").setValue("True");

                inputData.namedObjectField("Resource").setRef("10710003");

                var dSubList = inputData.subentityList("ServiceDetails");
                var dSub = dSubList.appendItem();

                var eSub = dSub.subentityList("Checklist");
                var fSub = eSub.appendItem();
                fSub.setAttribute("__listItemAction", "add");

                fSub.dataField("ChecklistId").setValue("传送带");
                fSub.dataField("Comments").setValue("222");
                fSub.dataField("Instruction").setValue("检查各位置传送带、Z轴同步带是否磨损是否变形");
                dSub.namedObjectField("MaintenanceStatus").setObjectId("001cb38000000162"); ;

            });

            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"设备保养执行");
            return resultStatus;
        }

        // 新增涉酸记录
        private void EquipmentAcidInvolved(List<AcidEmployee> acids, string userID, string sourceType)
        {
            try
            {
                string resourceid = "";
                if (acids.Count > 0)
                {
                    EquipmentRepository er = new EquipmentRepository();
                    resourceid = er.GetEquipmentIDByNo(acids[0].ResourceID);
                }
                NXmlClient nXmlClient = new NXmlClient();
                nXmlClient.NormalSubmit("jaAcidInvolvedAdd", inputData =>
               {
                   var csiAcidList = inputData.subentityList("EmployeeAcidInvolvedLis");
                   string curTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                   foreach (var item in acids)
                   {
                       var csiAcidItem = csiAcidList.appendItem();
                       //csiAcidItem.setObjectType("EmployeeAcidInvolvedHis");
                       // var csiSub =   csiAcidItem.subentityField("EmployeeAcidInvolvedHis");
                       csiAcidItem.dataField("AcidInvolvedEmployeeID").setValue(item.AcidInvolvedEmployeeID);
                       csiAcidItem.dataField("CreatEmployeeID").setValue(userID);
                       csiAcidItem.dataField("CreatTime").setFormattedValue(curTime, dataFormats.formatDate);
                       csiAcidItem.dataField("DefectFromID").setValue(item.DefectFromID);
                       csiAcidItem.dataField("Duration").setValue(item.Duration);
                       csiAcidItem.dataField("ResourceID").setValue(resourceid);
                       csiAcidItem.dataField("SourceType").setValue(sourceType);
                   }
               });
            }
            catch
            { }
        }

        public ResultStatus EquipmentAcidInvolvedTest()
        {
            ResultStatus resultStatus = new ResultStatus();
            NXmlClient nXmlClient = new NXmlClient();
            resultStatus = nXmlClient.NormalSubmit("jaAcidInvolvedAdd", inputData =>
            {
                var csiAcidList = inputData.subentityList("EmployeeAcidInvolvedLis");
                var csiAcidItem = csiAcidList.appendItem();
                //csiAcidItem.setObjectType("EmployeeAcidInvolvedHis");
                // var csiSub =   csiAcidItem.subentityField("EmployeeAcidInvolvedHis");
                csiAcidItem.dataField("AcidInvolvedEmployeeID").setValue("0004748000000004");
                csiAcidItem.dataField("CreatEmployeeID").setValue("0004748000000004");
                csiAcidItem.dataField("CreatTime").setFormattedValue("2021-06-29 09:00:00", dataFormats.formatDate);
                csiAcidItem.dataField("DefectFromID").setValue("001c9f8000000002");
                csiAcidItem.dataField("Duration").setValue("20");
                csiAcidItem.dataField("ResourceID").setValue("4880188000000002");
                csiAcidItem.dataField("SourceType").setValue("保养");


                var csiAcidItem2 = csiAcidList.appendItem();
                //csiAcidItem.setObjectType("EmployeeAcidInvolvedHis");
                // var csiSub =   csiAcidItem.subentityField("EmployeeAcidInvolvedHis");
                csiAcidItem2.dataField("AcidInvolvedEmployeeID").setValue("0004748000000004");
                csiAcidItem2.dataField("CreatEmployeeID").setValue("0004748000000004");
                csiAcidItem2.dataField("CreatTime").setValue("2021-06-26 09:00:00");
                csiAcidItem2.dataField("DefectFromID").setValue("001c9f8000000002");
                csiAcidItem2.dataField("Duration").setValue("30");
                csiAcidItem2.dataField("ResourceID").setValue("4880188000000002");
                csiAcidItem2.dataField("SourceType").setValue("保养");

            });

            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"涉酸");
            return resultStatus;
        }

        // 新增涉酸记录
        public ResultStatus EquipmentAcidInvolvedSingle()
        {
            ResultStatus resultStatus = new ResultStatus();
            NXmlClient nXmlClient = new NXmlClient();
            resultStatus = nXmlClient.NormalSubmit("jaAcidInvolvedAddSingle", inputData =>
            {
                inputData.dataField("AcidInvolvedEmployeeID").setValue("0004748000000004");
                inputData.dataField("CreatEmployeeID").setValue("0004748000000004");
                inputData.dataField("CreatTime").setValue("2021-06-25 09:00:00");
                inputData.dataField("DefectFromID").setValue("001c9f8000000002");
                inputData.dataField("Duration").setValue("20");
                inputData.dataField("ResourceID").setValue("4880188000000002");

            });

            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"涉酸");
            return resultStatus;
        }

        /// <summary>
        /// 设备点检提交
        /// </summary>
        /// <param name="submitSpotInspection"></param>
        /// <returns></returns>
        public ResultStatus SubmitSpotInspectionResult(SpotInspectionSubmitEntity submitSpotInspection)
        {
            ResultStatus resultStatus = new ResultStatus();

            NXmlClient nXmlClient = new NXmlClient();
            resultStatus = nXmlClient.NormalSubmit("CompleteMaintenance", inputData =>
            {
                inputData.revisionedObjectField("DataCollectionDef").setRef(submitSpotInspection.DataCollectionName, "1", false);
                inputData.dataField("ForceMaintenance").setValue("false");
                inputData.revisionedObjectField("MaintenanceReq").setRef(submitSpotInspection.MaintenanceReqName, "", true);

                var csiParametricData = inputData.namedObjectField("ParametricData");
                csiParametricData.setAttribute("__action", "create");
                csiParametricData.setObjectType("DataPointSummary");

                var csiDataPointList = csiParametricData.subentityList("DataPointDetails");

                foreach (var item in submitSpotInspection.DataPointList)
                {
                    var csiSub = csiDataPointList.appendItem();
                    csiSub.setAttribute("__listItemAction", "add");
                    csiSub.setObjectType("DataPointDetails");

                    var csiNSub = csiSub.namedSubentityField("DataPoint");

                    csiNSub.setName(item.DataPointName);
                    csiNSub.parentInfo().setObjectId(item.DataCollectionDefID);
                    csiNSub.parentInfo().setRevisionedObjectRef(item.DataCollectionDefName, "1", false);

                    csiSub.dataField("DataType").setValue(item.DataType);
                    csiSub.dataField("DataValue").setValue(item.DataValue);
                }


                csiParametricData.dataField("OverrideDataPointLimits").setValue("True");

                inputData.namedObjectField("Resource").setRef(submitSpotInspection.ResourceName);

                var csiServiceDetailsList = inputData.subentityList("ServiceDetails");
                var csiServiceDetail = csiServiceDetailsList.appendItem();

                var csiChecklist = csiServiceDetail.subentityList("Checklist");
                foreach (var item in submitSpotInspection.CheckListList)
                {
                    var fSub = csiChecklist.appendItem();
                    fSub.setAttribute("__listItemAction", "add");

                    fSub.dataField("ChecklistId").setValue(item.ChecklistID);
                    fSub.dataField("Comments").setValue(item.Comments);
                    fSub.dataField("Instruction").setValue(item.Instruction);
                }

                csiServiceDetail.namedObjectField("MaintenanceStatus").setObjectId(submitSpotInspection.MaintenanceStatusID); ;

            });

            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"设备保养执行");
            return resultStatus;
        }


        // 新增备件使用记录
        public void EquipmentMoveOrderUse(List<MoveOrderDetail> moveOrderDetails, string userName, string sourceType)
        {
            try
            {
                string resourceid = "";
                if (moveOrderDetails.Count > 0)
                {
                    EquipmentRepository er = new EquipmentRepository();
                    resourceid = er.GetEquipmentIDByNo(moveOrderDetails[0].ResourceID);
                }
                NXmlClient nXmlClient = new NXmlClient();
                nXmlClient.NormalSubmit("jaResourceSparePartsUseAdd", inputData =>
                {
                    string curTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    var csiAcidList = inputData.subentityList("ResourceSparePartsUseDetailLis");

                    foreach (var item in moveOrderDetails)
                    {
                        var csiAcidItem = csiAcidList.appendItem();
                        //csiAcidItem.setObjectType("ResourceSparePartsUseDetailHis");
                        // var csiSub =   csiAcidItem.subentityField("ResourceSparePartsUseDetailHis");
                        csiAcidItem.dataField("CreateTime").setFormattedValue(curTime, dataFormats.formatDate);
                        csiAcidItem.dataField("CreateUser").setValue(userName);
                        csiAcidItem.dataField("DefectFromID").setValue(item.DefectFromID);
                        csiAcidItem.dataField("MaterialNo").setValue(item.MaterialNo);
                        csiAcidItem.dataField("MoveOrderDetailID").setValue(item.MoveOrderDetailID);
                        csiAcidItem.dataField("MoveOrderInfoName").setValue(item.MoveOrderInfoName);
                        csiAcidItem.dataField("ResourceID").setValue(resourceid);
                        csiAcidItem.dataField("UseQty").setValue(item.UseQty.ToString());
                        csiAcidItem.dataField("SourceType").setValue(sourceType);
                        csiAcidItem.dataField("TotalBackQty").setValue("0");
                    }
                });
            }
            catch
            { }
        }

        // 备件使用-测试
        public ResultStatus EquipmentMoveOrderUseTest()
        {
            ResultStatus resultStatus = new ResultStatus();
            NXmlClient nXmlClient = new NXmlClient();
            resultStatus = nXmlClient.NormalSubmit("jaResourceSparePartsUseAdd", inputData =>
            {

                var csiAcidList = inputData.subentityList("ResourceSparePartsUseDetailLis");
                var csiAcidItem = csiAcidList.appendItem();
                //csiAcidItem.setObjectType("EmployeeAcidInvolvedHis");
                // var csiSub =   csiAcidItem.subentityField("EmployeeAcidInvolvedHis");
                csiAcidItem.dataField("CreateTime").setFormattedValue("2021-07-19 09:00:00", dataFormats.formatDate);
                csiAcidItem.dataField("CreateUser").setValue("0004748000000004");
                csiAcidItem.dataField("DefectFromID").setValue("0004748000000004");
                csiAcidItem.dataField("MaterialNo").setValue("10");
                csiAcidItem.dataField("MoveOrderDetailID").setValue("48000e800000001b");
                csiAcidItem.dataField("MoveOrderInfoName").setValue("001");
                csiAcidItem.dataField("ResourceID").setValue("0004748000000004");
                csiAcidItem.dataField("UseQty").setValue("2");
                csiAcidItem.dataField("SourceType").setValue("保养");
                csiAcidItem.dataField("TotalBackQty").setValue("0");


                var csiAcidItem2 = csiAcidList.appendItem();
                csiAcidItem2.dataField("CreateTime").setFormattedValue("2021-06-28 19:00:00", dataFormats.formatDate);
                csiAcidItem2.dataField("CreateUser").setValue("0004748000000004");
                csiAcidItem2.dataField("DefectFromID").setValue("0004748000000004");
                csiAcidItem2.dataField("MaterialNo").setValue("10");
                csiAcidItem2.dataField("MoveOrderDetailID").setValue("48000e800000001b");
                csiAcidItem2.dataField("MoveOrderInfoName").setValue("001");
                csiAcidItem2.dataField("ResourceID").setValue("0004748000000004");
                csiAcidItem2.dataField("UseQty").setValue("1");
                csiAcidItem2.dataField("SourceType").setValue("保养");
                csiAcidItem2.dataField("TotalBackQty").setValue("0");
            });

            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"备件");
            return resultStatus;
        }

        // 新增缺陷记录
        public void EquipmentDefect(List<DataPoint> defectList, string userName, string assignedMaintReqID)
        {
            try
            {
                // 生成缺陷单号：yyyyMMdd0001
                int defectNo = GetDefectNo();
                string defectNoDate = DateTime.Now.ToString("yyyyMMdd");
                ResultStatus resultStatus = new ResultStatus();
                NXmlClient nXmlClient = new NXmlClient();
                string curTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                nXmlClient.NormalSubmit(" jaEquipmentDefectAdd", inputData =>
                 {
                     var csiAcidList = inputData.subentityList("jaEquipmentDefectManagementLis");
                     foreach (var item in defectList)
                     {
                         var csiAcidItem = csiAcidList.appendItem();
                         //csiAcidItem.setObjectType("EmployeeAcidInvolvedHis");
                         // var csiSub =   csiAcidItem.subentityField("EmployeeAcidInvolvedHis");
                         csiAcidItem.dataField("AssignedMaintReqID").setValue(assignedMaintReqID);
                         csiAcidItem.dataField("CreateUser").setValue(userName);
                         csiAcidItem.dataField("CreateTime").setFormattedValue(curTime, dataFormats.formatDate);
                         csiAcidItem.dataField("DefectFromID").setValue(item.DatapointID);

                         csiAcidItem.dataField("DataType").setValue(item.DataType);
                         csiAcidItem.dataField("DataValue").setValue(item.DataValue);
                         csiAcidItem.dataField("DefectItem").setValue(item.DataPointName);
                         csiAcidItem.dataField("DefectNo").setValue($"{defectNoDate}{(defectNo++).ToString().PadLeft(4, '0')}");
                         csiAcidItem.dataField("Lowerlimit").setValue(item.Lowerlimit);
                         // csiAcidItem.dataField("PlannedCompletionTime").setValue("2021-06-30 09:00:00");
                         csiAcidItem.dataField("ResourceID").setValue(item.ResourceID);
                         // csiAcidItem.dataField("RepairOrder").setValue("20");
                         csiAcidItem.dataField("Status").setValue("SUBMIT"); //COMPLETED
                         csiAcidItem.dataField("Upperlimit").setValue(item.Upperlimit);
                         csiAcidItem.dataField("SourceType").setValue("保养");
                     }

                 });

            }
            catch
            { }
        }

        private int GetDefectNo()
        {
            XmlClient oClient = new XmlClient();
            string curTie = DateTime.Now.ToString("yyyyMMdd");
            string sql = "select max(defectno) as defectno from jaEquipmentDefectManagement where defectno like '{0}%'";
            sql = string.Format(sql, curTie);
            DataTable dt = oClient.AdhocQuery(sql);
            if (dt != null && dt.Rows.Count > 0)
            {
                string defectno = ConvertHelper.ToString(dt.Rows[0]["defectno"]);
                if (defectno.Length == 12)
                {
                    // 后四位
                    return ConvertHelper.ToInt(defectno.Substring(8, 4)) + 1;
                }
                else
                {
                    return 1;
                }
                // return ConvertHelper.ToInt(dt.Rows[0]["defectno"]) + 1;
                // return $"{curTie}{num.ToString().PadLeft(4, '0')}";
            }
            else
            {
                return 1;
                // return $"{curTie}0001";
            }
        }
        // 缺陷-测试
        public ResultStatus EquipmentDefectTest()
        {
            // 生成缺陷单号：yyyyMMdd0001
            int defectNo = GetDefectNo();
            string curTime = DateTime.Now.ToString("yyyyMMdd");
            ResultStatus resultStatus = new ResultStatus();
            NXmlClient nXmlClient = new NXmlClient();
            resultStatus = nXmlClient.NormalSubmit("jaEquipmentDefectAdd", inputData =>
            {
                var csiAcidList = inputData.subentityList("jaEquipmentDefectManagementLis");
                var csiAcidItem = csiAcidList.appendItem();
                //csiAcidItem.setObjectType("EmployeeAcidInvolvedHis");
                // var csiSub =   csiAcidItem.subentityField("EmployeeAcidInvolvedHis");
                csiAcidItem.dataField("AssignedMaintReqID").setValue("0004748000000004");
                csiAcidItem.dataField("CreateUser").setValue("0004748000000004");
                csiAcidItem.dataField("CreateTime").setFormattedValue("2021-06-30 09:00:00", dataFormats.formatDate);
                csiAcidItem.dataField("DefectFromID").setValue("001c9f8000000002");

                csiAcidItem.dataField("DataType").setValue("2");
                csiAcidItem.dataField("DataValue").setValue("20");
                csiAcidItem.dataField("DefectItem").setValue("传送带");
                csiAcidItem.dataField("DefectNo").setValue($"{curTime}{(defectNo++).ToString().PadLeft(4, '0')}");
                csiAcidItem.dataField("Lowerlimit").setValue("0");
                // csiAcidItem.dataField("PlannedCompletionTime").setValue("2021-06-30 09:00:00");
                csiAcidItem.dataField("ResourceID").setValue("4880188000000002");
                csiAcidItem.dataField("RepairOrder").setValue("20");
                csiAcidItem.dataField("Status").setValue("SUBMIT"); //COMPLETED
                csiAcidItem.dataField("Upperlimit").setValue("20");
                csiAcidItem.dataField("SourceType").setValue("保养");

            });

            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"缺陷");
            return resultStatus;
        }

        // 缺陷完成-测试
        public ResultStatus EquipmentDefectCompletedTest()
        {
            ResultStatus resultStatus = new ResultStatus();
            NXmlClient nXmlClient = new NXmlClient();
            resultStatus = nXmlClient.NormalSubmit("jaEquipmentDefectCompleted", inputData =>
            {
                inputData.dataField("AssignedMaintReqID").setValue("0004748000000005");
                inputData.dataField("ModifiedTime").setFormattedValue("2021-07-03 09:00:00", dataFormats.formatDate);
                inputData.dataField("ModifiedUser").setValue("12345");
                inputData.dataField("Status").setValue("COMPLETED");

                var csiAcidList = inputData.subentityList("jaEquipmentDefectManagementLis");
                var csiAcidItem = csiAcidList.appendItem();
                csiAcidItem.setObjectId("4800d18000000002"); // 4800d1800000001b

                var csiAcidItem2 = csiAcidList.appendItem();
                csiAcidItem2.setObjectId("4800d1800000001b");
                // csiAcidItem.namedSubentityField("jaEquipmentDefectManagement").setObjectId("4800d18000000002");
                // csiAcidItem.subentityField("jaEquipmentDefectManagement").setObjectId("4800d18000000002");

                // csiAcidItem.namedObjectField("jaEquipmentDefectManagement").setRef("52A9FEC2-9C4C-47f6-A105-765024FA700B");//.setObjectId("4800d18000000002");

            });

            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"缺陷");
            return resultStatus;
        }

        public ResultStatus EquipmentDefectCompleted(Defect defect)
        {
            ResultStatus resultStatus = new ResultStatus();
            NXmlClient nXmlClient = new NXmlClient();
            string curTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            resultStatus = nXmlClient.NormalSubmit("jaEquipmentDefectCompleted", inputData =>
            {
                inputData.dataField("AssignedMaintReqID").setValue(defect.AssignedMaintReqID);
                inputData.dataField("ModifiedTime").setFormattedValue(curTime, dataFormats.formatDate);
                inputData.dataField("ModifiedUser").setValue(defect.UserName);
                inputData.dataField("Status").setValue("COMPLETED");

                var csiAcidList = inputData.subentityList("jaEquipmentDefectManagementLis");
                foreach (var item in defect.DefectIDList)
                {
                    var csiAcidItem = csiAcidList.appendItem();
                    csiAcidItem.setObjectId(item); // 4800d1800000001b
                }
            });

            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"缺陷完成");
            return resultStatus;
        }

        #region

        // 设备保养执行
        public ResultStatus SpotInspectionExecute(EQPRequirementEntity eQPRequirementEntity)
        {
            ResultStatus resultStatus = new ResultStatus();

            string curTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            NXmlClient nXmlClient = new NXmlClient(); // jaCompleteMaintenance CompleteMaintenance
            resultStatus = nXmlClient.NormalSubmit("jaCompleteMaintenance", eQPRequirementEntity.UserName, eQPRequirementEntity.PassWord, inputData =>
            {
                if (eQPRequirementEntity.DataCollectionDefName != "")
                {
                    inputData.revisionedObjectField("DataCollectionDef").setRef(eQPRequirementEntity.DataCollectionDefName, "1", false);
                }
                inputData.dataField("ForceMaintenance").setValue("false");
                inputData.revisionedObjectField("MaintenanceReq").setRef(eQPRequirementEntity.MaintenanceReqName, "", true);

                if (eQPRequirementEntity.DataPointList != null && eQPRequirementEntity.DataPointList.Count > 0)
                {
                    var csiParametricData = inputData.namedObjectField("ParametricData");
                    csiParametricData.setAttribute("__action", "create");
                    csiParametricData.setObjectType("DataPointSummary");

                    var csiDataPointList = csiParametricData.subentityList("DataPointDetails");

                    foreach (var item in eQPRequirementEntity.DataPointList)
                    {
                        var csiSub = csiDataPointList.appendItem();
                        csiSub.setAttribute("__listItemAction", "add");
                        csiSub.setObjectType("DataPointDetails");

                        var csiNSub = csiSub.namedSubentityField("DataPoint");

                        csiNSub.setName(item.DataPointName);
                        csiNSub.parentInfo().setObjectId(item.DataCollectionDefID);
                        csiNSub.parentInfo().setRevisionedObjectRef(item.DataCollectionDefName, "1", false);

                        csiSub.dataField("DataType").setValue(item.DataType);
                        csiSub.dataField("DataValue").setValue(item.DataValue);
                    }


                    csiParametricData.dataField("OverrideDataPointLimits").setValue("True");
                }

                inputData.namedObjectField("Resource").setRef(eQPRequirementEntity.ResourceName);

                var csiServiceDetailsList = inputData.subentityList("ServiceDetails");
                var csiServiceDetail = csiServiceDetailsList.appendItem();

                var csiChecklist = csiServiceDetail.subentityList("Checklist");
                foreach (var item in eQPRequirementEntity.CheckListList)
                {
                    var fSub = csiChecklist.appendItem();
                    fSub.setAttribute("__listItemAction", "add");

                    fSub.dataField("ChecklistId").setValue(item.ChecklistID);
                    fSub.dataField("Comments").setValue(item.Comments);
                    fSub.dataField("Instruction").setValue(item.Instruction);
                    fSub.dataField("OverDueReason").setValue(eQPRequirementEntity.OverDueReason);
                }

                csiServiceDetail.namedObjectField("MaintenanceStatus").setObjectId(eQPRequirementEntity.MaintenanceStatusID);

                // 生成缺陷项
                if (eQPRequirementEntity.DataPointList != null && eQPRequirementEntity.DataPointList.Count > 0)
                {
                    List<DataPoint> defectList = new List<DataPoint>();
                    foreach (var item in eQPRequirementEntity.DataPointList)
                    {
                        // 检查是否合格
                        if (item.DataValue != "")
                        {
                            // 数据类型 1-Integer；2-Float；3-Fixed；4-String；5-Object；6-TimeStamp；7-Boolean；9-Decimal
                            if (item.DataType == "1" || item.DataType == "2" || item.DataType == "3" || item.DataType == "9")
                            {
                                // 数字值
                                if (ConvertHelper.ToDecimal(item.DataValue) < ConvertHelper.ToDecimal(item.Lowerlimit) || ConvertHelper.ToDecimal(item.DataValue) > ConvertHelper.ToDecimal(item.Upperlimit))
                                {
                                    defectList.Add(item);
                                }
                            }
                            else if (item.DataType == "7")
                            {
                                if (item.DataValue.ToUpper() == "FALSE")
                                {
                                    defectList.Add(item);
                                }
                            }
                        }
                    }
                    if (defectList != null && defectList.Count > 0)
                    {
                        int defectNo = GetDefectNo();
                        string defectNoDate = DateTime.Now.ToString("yyyyMMdd");
                        var csiAcidList = inputData.subentityList("jaEquipmentDefectManagementLis");
                        foreach (var item in defectList)
                        {
                            var csiAcidItem = csiAcidList.appendItem();
                            //csiAcidItem.setObjectType("EmployeeAcidInvolvedHis");
                            // var csiSub =   csiAcidItem.subentityField("EmployeeAcidInvolvedHis");
                            //csiAcidItem.dataField("AssignedMaintReqID").setValue(eQPRequirementEntity.AssignedMaintReqID);
                            csiAcidItem.dataField("CreateUser").setValue(eQPRequirementEntity.UserName);
                            csiAcidItem.dataField("CreateTime").setFormattedValue(curTime, dataFormats.formatDate);
                            csiAcidItem.dataField("DefectFromID").setValue(eQPRequirementEntity.MaintenanceReqID);

                            csiAcidItem.dataField("DataType").setValue(item.DataType);
                            csiAcidItem.dataField("DataValue").setValue(item.DataValue);
                            csiAcidItem.dataField("DefectItem").setValue(item.DataPointName);
                            csiAcidItem.dataField("DefectNo").setValue($"{defectNoDate}{(defectNo++).ToString().PadLeft(4, '0')}");
                            csiAcidItem.dataField("Lowerlimit").setValue(item.Lowerlimit);
                            // csiAcidItem.dataField("PlannedCompletionTime").setValue("2021-06-30 09:00:00");
                            csiAcidItem.dataField("ResourceID").setValue(item.ResourceID);
                            // csiAcidItem.dataField("RepairOrder").setValue("20");
                            csiAcidItem.dataField("Status").setValue("SUBMIT"); //COMPLETED
                            csiAcidItem.dataField("Upperlimit").setValue(item.Upperlimit);
                            csiAcidItem.dataField("SourceType").setValue("点检");
                        }
                    }
                }
            });

            resultStatus.ResultMsg = string.Concat(resultStatus.ResultMsg, $"设备点检执行");
            return resultStatus;
        }


        #endregion

        #region

        public ResultStatus EquipmentShortStopHistoryCreateExecute(EquipmentShortStopHistory entity)
        {
            ResultStatus resultStatus = new ResultStatus();
            resultStatus.ResultCode = "0";
            try
            {
                NXmlClient nXmlClient = new NXmlClient();
                resultStatus = nXmlClient.NormalSubmit("jaEquipmentShortStopHisCreate", entity.CreateEmployeeName, entity.PassWord, inputData =>
                {
                    var lstMoveOrderPara = inputData.subentityList("EquipmentShortStopHisParaList");
                    var moveOrderPara = lstMoveOrderPara.appendItem();

                    moveOrderPara.namedObjectField("Resource").setRef(entity.ResourceName);
                    moveOrderPara.namedObjectField("Shift").setRef(entity.ShiftName);
                    moveOrderPara.namedObjectField("Team").setRef(entity.TeamName);
                    moveOrderPara.dataField("ShutdownTime").setValue(entity.ShutdownTime);
                    moveOrderPara.namedObjectField("jaEquipmentShortStopReason").setRef(entity.EquipmentShortStopReasonName);
                    moveOrderPara.dataField("FaultDescription").setValue(entity.FaultDescription);
                    moveOrderPara.namedObjectField("SolveEmployee").setRef(entity.SolveEmployeeName);
                    moveOrderPara.dataField("Remark").setValue(entity.Remark);
                    moveOrderPara.namedObjectField("CreateEmployee").setRef(entity.CreateEmployeeName);

                });

                return resultStatus;

            }
            catch (Exception ex)
            {
                resultStatus.ResultMsg = ex.Message;
                return resultStatus;
            }
        }

        #endregion

        #region 生产过站接口
        /// <summary>
        /// 生产过站接口
        /// </summary>
        /// <param name="name">用户</param>
        /// <param name="password">密码</param>
        /// <param name="lstMoveInfo">物料主数据实体类列表</param>
        /// <returns></returns>
        public ResultStatus WIPSpecMoveStd(jaBTMoveInModel.RequestInfo BtInfo)
        {
            ResultStatus result = new ResultStatus();
            string message = string.Empty;
            string resource = string.Empty;
            string carrier = string.Empty;
            result.ResultCode = "0";
            EapInterfaceRepository Repository = new EapInterfaceRepository();

            string strGuid = string.Empty;
            try
            {
                //查询MES中设备,载具是否存在
                //XmlClient oClient = new XmlClient();
                //resource = Repository.GetResourceName(BtInfo.Equipment);
                //if (!string.IsNullOrEmpty(resource))
                //{
                string equipment = BtInfo.Equipment;
                result = SaveCarrier(BtInfo.Carrier, ref equipment);
                if (string.IsNullOrEmpty(equipment))
                {
                    result.ResultCode = "0";
                    result.ResultMsg = $"获取不到设备{BtInfo.Equipment} 对应的设备机台号";
                    return result;
                }
                BtInfo.Equipment = equipment;

                if (result.ResultCode == "0")
                    return result;

                switch (BtInfo.ActionCode)
                {
                    case "ZR_IN":
                        result = Repository.SaveBTMoveIn(BtInfo);
                        break;
                    case "ZR_OUT":
                        result = Repository.SaveBTMoveOut(BtInfo, strGuid);
                        break;
                    case "KS_IN":
                        result = Repository.SaveKSMoveIn(BtInfo);
                        break;
                    #region 注释
                    //case "KS_IN_BOAT":
                    //    result = Repository.SaveKSMoveInBOAT(BtInfo);
                    //    //message = "扩散花篮进舟完成";
                    //    break;
                    //case "KS_OUT_BOAT":
                    //        result = Repository.SaveKSMoveOutBOAT(BtInfo); //进舟管状态为空,以舟号设备号 查询不同的guid
                    //                                                       //message = "扩散舟出炉管";
                    //    break;
                    #endregion
                    case "KS_OUT":
                        result = Repository.SaveKSMoveOut(BtInfo, strGuid);
                        break;
                    case "SE_IN":
                        result = Repository.SEMoveIn(BtInfo);
                        //message = "SE花篮进站";
                        break;
                    case "SE_OUT":
                        result = Repository.SEMoveOut(BtInfo);
                        //message = "SE花篮出站";
                        break;
                    case "PSG_IN":
                        result = Repository.PSGMoveIn(BtInfo);
                        //message = "链氧去SPG二合一花篮进";
                        break;
                    case "PSG_OUT":
                        result = Repository.PSGMoveOut(BtInfo);
                        //message = "链氧去SPG二合一花篮出站";
                        break;
                    #region 碱抛工序操作
                    case "JP_IN":
                        strGuid = Repository.GetPSGcarrierMoveIn(BtInfo);//查询去SPG花篮进的wafer
                        if (!string.IsNullOrEmpty(strGuid))
                        {
                            result = Repository.JPMoveIN(BtInfo, strGuid);
                            //message = "碱抛花篮进";
                        }
                        else
                            result.ResultMsg = "未找到碱抛花篮进!";
                        break;
                    case "JP_OUT":
                        strGuid = Repository.GetPSGcarrierMoveIn(BtInfo);//查询去SPG花篮进的wafer
                        if (!string.IsNullOrEmpty(strGuid))
                        {
                            result = Repository.JPMoveOut(BtInfo, strGuid);
                            //message = "碱抛花篮出站";
                        }
                        else
                            result.ResultMsg = "未找到去SPG花篮进!";
                        break;
                    #endregion
                    case "TH_IN":
                        result = Repository.THMoveIn(BtInfo);
                        //message = "退火花篮进";
                        break;
                    case "TH_IN_BOAT":
                        result = Repository.THBoatMoveIn(BtInfo);
                        //message = "退火花篮进舟完成";
                        break;
                    case "TH_OUT_BOAT":
                        result.ResultCode = "1";
                        result.ResultMsg = "退火舟出炉管完成";
                        //result = Repository.THBoatMoveOut(BtInfo);
                        break;
                    case "TH_OUT":
                        result = Repository.THMoveOut(BtInfo);
                        //message = "退火花篮出站";
                        break;
                    case "BM_IN":
                        result = Repository.BMMoveIn(BtInfo);
                        //message = "背膜花篮进";
                        break;
                    case "BM_IN_BOAT":
                        result = Repository.BMBoatMoveIn(BtInfo);
                        //message = "背膜舟装片完成";
                        break;
                    case "BM_OUT_BOAT":
                        //result = Repository.BMBoatMoveOut(BtInfo);
                        result.ResultCode = "1";
                        result.ResultMsg = "背膜舟出炉管完成";
                        break;
                    case "BM_OUT":
                        result = Repository.BMMoveOut(BtInfo);
                        //message = "背膜花篮出站";
                        break;
                    case "ZM_IN":
                        result = Repository.ZMMoveIn(BtInfo);
                        break;
                    case "ZM_IN_BOAT":
                        result = Repository.ZMBoatMoveIn(BtInfo);
                        break;
                    case "ZM_OUT_BOAT":
                        //result = Repository.ZMBoatMoveOut(BtInfo);
                        result.ResultCode = "1";
                        result.ResultMsg = "正膜舟出炉管完成";
                        break;
                    case "ZM_OUT":
                        result = Repository.ZMMoveOut(BtInfo);
                        //message = "正膜花篮出站";
                        break;
                    case "JG_IN":
                        result = Repository.JGMoveIn(BtInfo);
                        //message = "激光开槽花篮进";
                        break;
                        //case "SZ_OUT":
                        //    result = Repository.SZMoveOut(BtInfo);
                        //    message = "烧结出";
                        //    break;
                        //case "DZR_OUT":
                        //    result = Repository.DZRMoveOut(BtInfo);
                        //    message = "电注入出";
                        //    break;
                        //case "SW_OUT":
                        //    result = Repository.SWMoveOut(BtInfo);
                        //    //message = "分选下料";
                        //    break;
                }
            }
            catch (Exception ex)
            {
                //异常处理
                //记录生产过站处理失败的数据
                //Repository.InsertWipMoveInlog(BtInfo);
                LogHelper.Error("jaWIPSpec接口" + result.ResultMsg + ex.Message);
            }
            return result;
        }
        /// <summary>
        /// 分选下料接口数据处理
        /// </summary>
        /// <param name="BtInfo"></param>
        /// <returns></returns>
        public ResultStatus JaTestMoveOut(jaBTMoveInModel BtInfo)
        {
            ResultStatus result = new ResultStatus();
            string message = string.Empty;
            string resource = string.Empty;
            string carrier = string.Empty;
            result.ResultCode = "0";
            EapInterfaceRepository Repository = new EapInterfaceRepository();

            string strGuid = string.Empty;
            try
            {
                //判断设备是否存在
                string equipment = BtInfo.requestInfo.Equipment;
                result = SaveCarrier(BtInfo.requestInfo.Carrier, ref equipment);
                if (string.IsNullOrEmpty(equipment))
                {
                    result.ResultCode = "0";
                    result.ResultMsg = $"获取不到设备{BtInfo.requestInfo.Equipment} 对应的设备机台号";
                    return result;
                }
                BtInfo.requestInfo.Equipment = equipment;
                if (result.ResultCode == "0")
                    return result;
                else
                    result = Repository.SWMoveOut(BtInfo);
            }
            catch (Exception ex)
            {
                //异常处理
                //记录生产过站处理失败的数据
                //Repository.InsertWipMoveInlog(BtInfo);
                LogHelper.Error("JaTestMoveOut" + result.ResultMsg + ex.Message);
            }
            return result;

        }
        #endregion

        #region "保存载具"
        /// <summary>
        /// MoveIn
        /// </summary>
        /// <param name="mHostInfo">登录信息</param>
        /// <param name="strContainerName">花篮号</param>
        /// <returns></returns>
        public ResultStatus SaveCarrier(string CarrierName, ref string resource)
        {
            ResultStatus oResult = new ResultStatus();
            oResult.ResultCode = "1";
            try
            {
                XmlClient oClient = new XmlClient();
                EapInterfaceRepository Repository = new EapInterfaceRepository();
                DataTable dt = Repository.GetSpecDescByResource(resource);
                resource = string.Empty;


                //载具在系统中是否存在
                if (!Repository.QueryCarrier(CarrierName))
                {
                    List<CamstarApiEntity> m_DataList = new List<CamstarApiEntity>();
                    CamstarApiEntity dataEntity = new CamstarApiEntity();

                    dataEntity = new CamstarApiEntity();
                    dataEntity.ClientDataTypeEnum = DataTypeEnum.DataField;
                    dataEntity.ClientDataName = "Name";
                    dataEntity.ClientDataValue = CarrierName;
                    m_DataList.Add(dataEntity);

                    dataEntity = new CamstarApiEntity();
                    dataEntity.ClientDataTypeEnum = DataTypeEnum.NamedObjectField;
                    dataEntity.ClientDataName = "Factory";
                    dataEntity.ClientDataValue = "C1";
                    m_DataList.Add(dataEntity);

                    if (dt != null && dt.Rows.Count > 0)
                    {
                        resource = dt.Rows[0]["subdescription"].ToString();
                        string description = dt.Rows[0]["SPECNAME"].ToString();
                        if (description == "前道")
                        {
                            dataEntity = new CamstarApiEntity();
                            dataEntity.ClientDataTypeEnum = DataTypeEnum.NamedObjectField;
                            dataEntity.ClientDataName = "ResourceFamily";
                            dataEntity.ClientDataValue = "湿花篮";
                            m_DataList.Add(dataEntity);
                        }
                        else
                        {
                            dataEntity = new CamstarApiEntity();
                            dataEntity.ClientDataTypeEnum = DataTypeEnum.NamedObjectField;
                            dataEntity.ClientDataName = "ResourceFamily";
                            dataEntity.ClientDataValue = "干花篮";
                            m_DataList.Add(dataEntity);
                        }
                    }
                    CamstarClientAPI oApi = new CamstarClientAPI();
                    oResult = oApi.CreateModelByStandard(ref oClient, "CarrierMaintDoc", "CarrierMaint", m_DataList);
                }
                else
                {
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        resource = dt.Rows[0]["subdescription"].ToString();
                    }
                }
                return oResult;
            }
            catch (Exception ex)
            {
                oResult.ResultMsg = ex.Message;
                return oResult;
            }
        }
        #endregion



        #region WIPSpec
        /// <summary>
        /// 生产过站接口
        /// </summary>
        /// <param name="name">用户</param>
        /// <param name="password">密码</param>
        /// <param name="BtInfo">实体类列表</param>
        /// <returns></returns>
        //public EAPSubmitResultInfo WIPSpecMoveStd(jaBTMoveInModel.RequestInfo BtInfo, out string datacontrol)
        //{
        //    EAPSubmitResultInfo lstSubmitResult = new EAPSubmitResultInfo()
        //    {
        //        resultInfo = new EAPSubmitResultInfo.ResultInfo()
        //        {
        //            ROWS = new List<EAPSubmitResultInfo.ROWS>()
        //        }
        //    };
        //    datacontrol = "";
        //    ResultStatus oResult = new ResultStatus();
        //    bool result = false;
        //    string message = string.Empty;
        //    string resource = string.Empty;
        //    EapInterfaceRepository Repository = new EapInterfaceRepository();
        //    EAPSubmitResultInfo.ROWS oWS = new EAPSubmitResultInfo.ROWS();
        //    string strGuid = Guid.NewGuid().ToString();

        //    try
        //    {
        //        //查询MES中设备,载具是否存在
        //        resource = Repository.GetResourceName(BtInfo.Equipment);
        //        if (!string.IsNullOrEmpty(resource))
        //        {
        //            result = Repository.InsertMoveInInfo(BtInfo, strGuid);
        //            if (result == true)
        //            {
        //                oWS.RESPONSE_CODE = "SUCCESS";
        //                oWS.RESPONSE_MSG = message + "处理成功";
        //                lstSubmitResult.resultInfo.ROWS.Add(oWS);
        //            }
        //            else
        //            {
        //                //datacontrol += moveInfo.REQUISITION + " ";
        //                oWS.RESPONSE_CODE = "ERROR";
        //                oWS.RESPONSE_MSG = message + "处理报错";// + oResult.ResultMsg;
        //                datacontrol += oWS.RESPONSE_MSG + " ";
        //                lstSubmitResult.resultInfo.ROWS.Add(oWS);
        //                //记录制绒进站处理失败的数据
        //                //Repository.InsertWipMoveInlog(BtInfo);
        //            }
        //        }
        //        else
        //        {
        //            oWS.RESPONSE_CODE = "ERROR";
        //            oWS.RESPONSE_MSG = BtInfo.Equipment + "在MES系统中不存在,请确认";
        //            lstSubmitResult.resultInfo.ROWS.Add(oWS);
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        //异常处理
        //        oWS.RESPONSE_CODE = "ERROR";
        //        oWS.RESPONSE_MSG = message + ex.Message;
        //        datacontrol += oWS.RESPONSE_MSG + " ";
        //        lstSubmitResult.resultInfo.ROWS.Add(oWS);
        //        //记录制绒进站异常数据
        //        //Repository.InsertWipMoveInlog(BtInfo);
        //    }

        //    return lstSubmitResult;
        //}
        #endregion

        /// <summary>
        /// 保存IV测试数据
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public bool InsertIVTestData(List<IVTestModel> lstIV)
        {
            bool result = false;
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                var _unitofWork2 = new UnitOfWork<JAIVTESTDATA>();
                Mapper.CreateMap<IVTestModel, JAIVTESTDATA>();
                List<JAIVTESTDATA> waffers = lstIV.Select(p => Mapper.Map<IVTestModel, JAIVTESTDATA>(p)).ToList();
                foreach (var lot in waffers)
                {
                    DataTable dt = Repository.GetSpecDescByResource(lot.EQUIPMENT_ID);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        lot.FACTORY = dt.Rows[0]["FACTORYNAME"].ToString();
                        lot.SPEC = dt.Rows[0]["SPECNAME"].ToString();
                    }
                }
                result = _unitofWork2.MIDRepository.Insert(waffers).Result;
            }
            catch (Exception ex)
            {
                //异常处理
            }
            return result;
        }

        /// <summary>
        /// 发料接口
        /// </summary>
        /// <param name="lstIV"></param>
        /// <param name="datacontrol"></param>
        /// <returns></returns>
        public bool SendMaterialData(MaterialLotModel.RequestInfo model)
        {
            bool result = false;
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            string guid = Guid.NewGuid().ToString();
            var _unitOfWork = new UnitOfWork();
            try
            {
                string sql = "";
                if (model.BoxList == null)
                {
                    return false;
                }
                DataTable dt = Repository.GetSpecDescByResource(model.Equipment);
                if (string.IsNullOrEmpty(dt.Rows[0]["FACTORYNAME"].ToString()))
                {//设备异常
                    return false;
                }
                for (int i = 0; i < model.BoxList.Count; i++)
                {
                    if (!string.IsNullOrEmpty(sql))
                        sql += " union all ";
                    sql += $"select '{guid}','{dt.Rows[0]["FACTORYNAME"].ToString()}','{model.Equipment}','{model.LotNo}','{model.TrayNo}','{model.LotQty}','{model.OrderType}','{model.WaferType}','{model.BoxList[i].BoxId.ToString()}','{model.WaferQty}',sysdate  from  dual";
                }
                string strSQL = @"INSERT INTO MaTerialLotDetailModel " + sql;

                result = _unitOfWork.MIDRepository.Insert(strSQL).Result > 0;
            }
            catch (Exception ex)
            {
                //异常处理
            }
            return result;
        }


        /// <summary>
        /// 舟进缓存台接口
        /// </summary>
        /// <param name="model"></param>
        /// <param name="datacontrol"></param>
        /// <returns></returns>
        public bool SaveBoatBuff(BoatInbufferModel.RequestInfo model)
        {
            bool result = false;
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                result = Repository.SaveBoatBuffdata(model);
            }
            catch (Exception ex)
            {
                //异常处理
            }
            return result;
        }

        /// <summary>
        /// 发料接口
        /// </summary>
        /// <param name="model"></param>
        /// <param name="datacontrol"></param>
        /// <returns></returns>
        public bool PrintQtyInfo(PrintQtyModel.RequestInfo model)
        {
            bool result = false;
            string factory = string.Empty;
            string spec = string.Empty;
            string ResourceDescription = string.Empty;
            string SubDescr = string.Empty;
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                string Equipment = Repository.GetResourceName(model.Equipment);
                if (!string.IsNullOrEmpty(Equipment))
                {
                    DataTable dt = Repository.GetSpecDescByResource(model.Equipment);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        factory = dt.Rows[0]["FACTORYNAME"].ToString() ?? null;
                        spec = dt.Rows[0]["SPECNAME"].ToString() ?? null;
                        ResourceDescription = dt.Rows[0]["DESCRIPTION"].ToString() ?? null;
                        SubDescr = dt.Rows[0]["SUBDESCRIPTION"].ToString() ?? null;
                    }

                    int CURRENTDAYOUTPUT = 0;
                    int CURRENTDAYINPUT = 0;
                    if (BlongDayShift())//白班
                    {
                        CURRENTDAYOUTPUT = Convert.ToInt32(model.OutputQty);
                        CURRENTDAYINPUT = Convert.ToInt32(model.InputQTY);
                    }
                    else//夜班
                    {
                        CURRENTDAYOUTPUT = Convert.ToInt32(model.OutputQty) + Convert.ToInt32(model.TotalOutputQty);
                        CURRENTDAYINPUT = Convert.ToInt32(model.InputQTY) + Convert.ToInt32(model.TotalInputQty);
                    }

                    // 补充其他字段值
                    model.Factory = factory;
                    model.Spec = spec;
                    model.Description = ResourceDescription;
                    model.SubDescription = SubDescr;
                    model.CurrentDayOutput = CURRENTDAYOUTPUT;
                    model.CurrentDayInput = CURRENTDAYINPUT;
                    model.CreateDate = _dateTimeProvider.Now;
                    model.TotalInQty = model.TotalInputQty.IsNumeral() ? Convert.ToInt32(model.TotalInputQty) : 0;
                    model.TotalOutQty = model.TotalOutputQty.IsNumeral() ? Convert.ToInt32(model.TotalOutputQty) : 0;

                    result = Repository.SavePrintQtyInfo(model);

                    // 如果保存成功，执行产量计算
                    if (result && dt?.Rows[0]["LIFETIME"].ToString() == "产能")
                    {
                        // 调用产量计算功能
                        if (model.Spec == "丝网")
                        {
                            //1. 生成测试分选的数据
                            {
                                var _tModel = model.Copy().ConvertToEntity<PrintQtyModel.RequestInfo>();
                                _tModel.Spec = "测试分选";
                                _tModel.SubDescription = "测试分选" + _tModel.SubDescription.Substring(5);
                                CalculateAndSaveEquipmentHourlyOutput(_tModel);
                            }
                            //2. 生成丝网的数据
                            {
                                var _sModel = model.Copy().ConvertToEntity<PrintQtyModel.RequestInfo>();
                                _sModel.OutputQty = _sModel.InputQTY;
                                _sModel.TotalOutputQty = _sModel.TotalInputQty;
                                _sModel.TotalOutQty = _sModel.TotalInQty;
                                CalculateAndSaveEquipmentHourlyOutput(_sModel);
                            }
                        }
                        else
                        {
                            CalculateAndSaveEquipmentHourlyOutput(model);
                        }
                    }
                }
                else
                    result = false;
            }
            catch (Exception ex)
            {
                //异常处理
            }
            return result;
        }

        /// <summary>
        /// 计算并保存设备小时产量
        /// </summary>
        /// <param name="model">产量数据</param>
        private void CalculateAndSaveEquipmentHourlyOutput(PrintQtyModel.RequestInfo model)
        {
            try
            {
                // 获取关键数据
                string equipmentId = model.SubDescription;

                // 计算时间点（整点后30分钟）- 使用注入的时间提供者
                DateTime now = model.CreateDate;

                DateTime timePoint = GetTimePoint(now);

                // 构建Redis键值
                string redisKey = $"EQP_OUTPUT:{equipmentId}";
                string timePointStr = timePoint.ToString("HH:mm");

                // 获取Redis客户端
                var redisClient = DataAccessor.RedisProvider.RedisClient.Init;
                var redisData = GetEqpOutputData(equipmentId, now, redisClient, redisKey);

                if (redisData != null)
                {
                    // 存在当前时间点的上一笔数据，
                    // 1. 不要判断是否特殊时间点，即不用修改上一个班次的最终产能，因为上一笔必须处理过，
                    // 2. 不用与上一个时间段数据做比较
                    // 计算小时产量
                    int hourlyOutput = 0;
                    if (model.OutputQty >= redisData.OutputQty)
                    {
                        // 正常数据，差值计算
                        hourlyOutput = model.OutputQty - redisData.OutputQty + redisData.HourlyOutput;
                    }
                    else
                    {
                        // 异常数据，可能是计数器重置
                        hourlyOutput = model.OutputQty + redisData.HourlyOutput;
                    }
                    // 更新数据库和Redis
                    SaveEqpOutputByHour(equipmentId, timePoint, new TimePointData
                    {
                        HourlyOutput = hourlyOutput,
                        OutputQty = model.OutputQty,
                        UpdateTime = now
                    }, redisClient, redisKey);
                }
                else
                {
                    // 不存在当前时间点的上一笔数据，
                    // 1. 判断是否特殊时间点，即不用修改上一个班次的最终产能，因为上一笔必须处理过，
                    // 判断是否为特殊时间点 (8:30 或 20:30)
                    bool isSpecialTimePoint = timePointStr == "08:30" || timePointStr == "20:30";
                    if (isSpecialTimePoint)
                    {
                        // 特殊时间点，
                        // 1. 修改上一班次的output 和产量
                        {
                            // 获取上一时间点
                            DateTime prevTimePoint = timePoint.AddHours(-1);
                            // 获取上一时间点的产量数据
                            var prevTimePointData = GetEqpOutputData(equipmentId, prevTimePoint, redisClient, redisKey);
                            if (prevTimePointData != null)
                            {
                                // 更新上一时间点的产量数据
                                SaveEqpOutputByHour(equipmentId, prevTimePoint, new TimePointData
                                {
                                    HourlyOutput = model.TotalOutQty - prevTimePointData.OutputQty + prevTimePointData.HourlyOutput,
                                    OutputQty = model.TotalOutQty,
                                    UpdateTime = now
                                }, redisClient, redisKey);
                            }
                        }

                        // 2. 与totalOutQty比较，获取当前时间段的产能
                        if (model.OutputQty > model.TotalOutQty)
                        {
                            // 还未清零
                            SaveEqpOutputByHour(equipmentId, timePoint, new TimePointData
                            {
                                HourlyOutput = model.OutputQty - model.TotalOutQty,
                                OutputQty = model.OutputQty,
                                UpdateTime = now
                            }, redisClient, redisKey);
                        }
                        else
                        {
                            SaveEqpOutputByHour(equipmentId, timePoint, new TimePointData
                            {
                                HourlyOutput = model.OutputQty,
                                OutputQty = model.OutputQty,
                                UpdateTime = now
                            }, redisClient, redisKey);
                        }

                    }
                    else
                    {
                        // 获取上一时间点
                        DateTime prevTimePoint = timePoint.AddHours(-1);
                        // 获取上一时间点的产量数据
                        var prevTimePointData = GetEqpOutputData(equipmentId, prevTimePoint, redisClient, redisKey);
                        int hourlyOutput = 0;
                        if (prevTimePointData != null)
                        {
                            // 2. 与上一个时间段数据做比较
                            if (model.OutputQty >= prevTimePointData.OutputQty)
                            {
                                // 正常数据，差值计算
                                hourlyOutput = model.OutputQty - prevTimePointData.OutputQty;
                            }
                            else
                            {
                                // 异常数据，可能是计数器重置
                                hourlyOutput = model.OutputQty;
                            }
                        }
                        else
                        {
                            hourlyOutput = model.OutputQty;
                        }
                        SaveEqpOutputByHour(equipmentId, timePoint, new TimePointData
                        {
                            HourlyOutput = hourlyOutput,
                            OutputQty = model.OutputQty,
                            UpdateTime = now
                        }, redisClient, redisKey);
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误日志
                LogHelper.Error($"计算设备{model.Equipment}产量出错：{ex.Message}", ex);
            }
        }

        private DateTime GetTimePoint(DateTime time)
        {
            DateTime timePoint = new DateTime(time.Year, time.Month, time.Day, time.Hour, 0, 0);

            // 修改时间点计算逻辑
            // 白班: 7:30-19:29, 夜班: 19:30-次日7:29
            // 7:30-8:29 算作 8:30, 8:30-9:29 算作 9:30, 以此类推
            if (time.Minute > 30)
            {
                timePoint = timePoint.AddHours(1);
            }
            timePoint = timePoint.AddMinutes(30);
            return timePoint;
        }

        private TimePointData GetEqpOutputData(string equipmentId, DateTime now, RedisClient redisClient, string redisKey)
        {
            DateTime timePoint = GetTimePoint(now);

            // 构建Redis键值
            string timePointStr = timePoint.ToString("HH:mm");

            // 从Redis获取当前时间点和上一时间点的数据
            var redisData = redisClient.HashGet<TimePointData>(redisKey, timePointStr);

            // 如果Redis中没有数据，从数据库加载历史数据到Redis
            if (redisData == null)
            {
                // 先判断redis中是否存在这个key，如果存在，说明之前插入过，不要再查数据库了，避免每次切换班次时，都要从数据库加载历史数据到Redis，提高效率。
                // 并且第一笔插入的时间点不可能刚好是8:30，所以如果存在这个key，说明之前插入过，不要再查数据库了。
                if (redisClient.KeyExists(redisKey))
                {
                    return null;
                }

                // 加载历史数据到Redis
                redisData = LoadHistoryDataToRedis(equipmentId, now, redisClient, redisKey);
            }
            return redisData;
        }

        /// <summary>
        /// 保存EQPOUTPUTBYHOUR表数据，并更新Redis
        /// 如果记录存在则更新，不存在则插入
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="timePoint">时间点</param>
        /// <param name="timePointData">时间点数据</param>
        /// <param name="redisClient">Redis客户端</param>
        /// <param name="redisKey">Redis键</param>
        private void SaveEqpOutputByHour(string equipmentId, DateTime timePoint, TimePointData timePointData, RedisClient redisClient, string redisKey)
        {
            try
            {
                // 获取班次信息
                string shift = timePoint.Hour >= 8 && timePoint.Hour < 20 ? "白班" : "夜班";

                // 判断是否特殊时间段，如果是，则存数据库的时候 NowDay 记得-1天
                string nowDay = (timePoint.Hour < 8 ? timePoint.AddDays(-1) : timePoint).ToString("yyyy-MM-dd");

                using (var db = new DataAccessor.BaseRepository(DataAccessor.DBProvider.MidDbProvider).GetSqlSugarClient())
                {
                    // 查询是否存在记录
                    var existingRecord = db.Queryable<Entities.Models.EMS.EQPOUTPUTBYHOUR2>()
                        .Where(x => x.EQUIPMENT == equipmentId &&
                               x.NOWDAY == nowDay &&
                               x.HOURS1 == timePoint.ToString("HH:mm"))
                        .OrderByDescending(x => x.CREATEDATE)
                        .First();

                    if (existingRecord != null)
                    {
                        // 更新记录
                        existingRecord.OUTPUT = timePointData.HourlyOutput.ToString();
                        existingRecord.SHIFT = shift;
                        existingRecord.CREATEDATE = timePointData.UpdateTime;

                        // 执行更新，使用条件更新而不是基于ID更新
                        db.Updateable(existingRecord)
                            .Where(x => x.EQUIPMENT == existingRecord.EQUIPMENT &&
                                  x.NOWDAY == existingRecord.NOWDAY &&
                                  x.HOURS1 == existingRecord.HOURS1)
                            .ExecuteCommand();
                    }
                    else
                    {
                        // 如果不存在记录，则插入新记录
                        var newRecord = new Entities.Models.EMS.EQPOUTPUTBYHOUR2
                        {
                            EQUIPMENT = equipmentId,
                            NOWDAY = nowDay,
                            HOURS1 = timePoint.ToString("HH:mm"),
                            HOURS2 = timePoint.ToString("MMdd HH:mm"),
                            OUTPUT = timePointData.HourlyOutput.ToString(),
                            SHIFT = shift,
                            CREATEDATE = timePointData.UpdateTime
                        };

                        // 执行插入
                        db.Insertable(newRecord).ExecuteCommand();
                    }
                }

                // 更新Redis
                string timePointStr = timePoint.ToString("HH:mm");
                redisClient.HashSet(redisKey, new Dictionary<string, TimePointData> { { timePointStr, timePointData } }, TimeSpan.FromDays(7));

                // 获取Redis中所有数据，只保留最新的三笔数据
                try
                {
                    // 获取所有时间点数据
                    var allTimePoints = redisClient.HashGet(redisKey);
                    if (allTimePoints != null && allTimePoints.Count > 3)
                    {
                        // 当前时间点的分钟数（从00:00开始计算）
                        int currentHour = timePoint.Hour;

                        // 按照时间点顺序排序（考虑跨天情况）
                        var sortedTimePoints = allTimePoints.Keys
                            .Select(key => new
                            {
                                Key = key,
                                SortOrder = GetTimePointSortOrder(key, currentHour)
                            })
                            .OrderBy(x => x.SortOrder)
                            .Select(x => x.Key)
                            .ToList();

                        // 保留最新的三个时间点（列表末尾的三个）
                        var keysToKeep = sortedTimePoints.Skip(Math.Max(0, sortedTimePoints.Count - 3)).ToList();

                        // 删除多余的键
                        foreach (var key in allTimePoints.Keys.Except(keysToKeep))
                        {
                            // 从Redis中删除此时间点的数据
                            redisClient.HashSet(redisKey, new Dictionary<string, TimePointData> { { key, null } }, TimeSpan.FromDays(7));
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.Error($"清理Redis数据时出错：{ex.Message}", ex);
                }
            }
            catch (Exception ex)
            {
                // 记录错误日志
                LogHelper.Error($"保存设备{equipmentId}在{timePoint}的产量数据出错：{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取时间点的排序顺序，用于处理跨天的时间点排序
        /// 由于时间点都是整点后30分钟的格式（如08:30、09:30），可以直接比较小时数
        /// </summary>
        /// <param name="timeStr">时间字符串，格式为"HH:30"</param>
        /// <param name="currentMinutes">当前时间点的分钟数（从00:00开始计算）</param>
        /// <returns>排序顺序值</returns>
        private long GetTimePointSortOrder(string timeStr, int currentHour)
        {
            // 由于分钟部分固定为30，直接解析小时部分
            int hour = int.Parse(timeStr.Substring(0, 2));

            if (hour <= currentHour)
            {
                // 同一天的时间点
                return hour;
            }
            else
            {
                // 前一天的时间点，减去24小时使其排在前面
                return hour - 24;
            }
        }

        /// <summary>
        /// 获取JAPRINTQTYINFO中小时=7,分钟>30的按EQUIPMENT分组的第一笔且OUTPUTQTY>1000的记录
        /// </summary>
        /// <returns>符合条件的设备产量数据列表</returns>
        public List<Entities.Models.Eap.PrintQtyModel.RequestInfo> GetMorningShiftFirstRecords()
        {
            try
            {
                using (var db = new DataAccessor.BaseRepository(DataAccessor.DBProvider.MidDbProvider).GetSqlSugarClient())
                {
                    // 使用窗口函数获取每个设备在7点30分后的第一笔记录，并过滤OutputQty>1000
                    // 根据现有代码中的查询模式，使用混合大小写字段名
                    var sql = @"
                        SELECT * FROM (
                            SELECT
                                GUID,
                                Equipment,
                                InputQTY,
                                OutputQty,
                                InProcessQty,
                                CreateDate,
                                Factory,
                                Spec,
                                ROW_NUMBER() OVER (PARTITION BY Equipment ORDER BY CreateDate ASC) as rn
                            FROM JAPRINTQTYINFO
                            WHERE EXTRACT(HOUR FROM CreateDate) = 7
                              AND EXTRACT(MINUTE FROM CreateDate) > 30
                        ) ranked_data
                        WHERE rn = 1 AND OutputQty > 1000";

                    var results = db.Ado.SqlQuery<Entities.Models.Eap.PrintQtyModel.RequestInfo>(sql);

                    LogHelper.Info($"获取到{results?.Count ?? 0}条符合条件的早班首笔产量记录");

                    return results ?? new List<Entities.Models.Eap.PrintQtyModel.RequestInfo>();
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"获取早班首笔产量记录出错：{ex.Message}", ex);
                return new List<Entities.Models.Eap.PrintQtyModel.RequestInfo>();
            }
        }

        /// <summary>
        /// 从数据库加载历史数据到Redis
        /// </summary>
        /// <param name="equipmentId">设备ID</param>
        /// <param name="timePoint">待查询数据的时间点</param>
        /// <param name="redisClient">Redis客户端</param>
        /// <param name="redisKey">Redis键</param>
        /// <returns>时间点数据</returns>
        private TimePointData LoadHistoryDataToRedis(string equipmentId, DateTime now, DataAccessor.RedisProvider.RedisClient redisClient, string redisKey)
        {
            DateTime timePoint = GetTimePoint(now);

            // 创建默认的TimePointData
            TimePointData timePointData = null;

            try
            {
                using (var db = new DataAccessor.BaseRepository(DataAccessor.DBProvider.MidDbProvider).GetSqlSugarClient())
                {
                    // 从EQPOUTPUTBYHOUR表查询历史数据
                    var _nowDay = (timePoint.Hour < 7 ? timePoint.AddDays(-1) : timePoint).ToString("yyyy-MM-dd");
                    var _hour = timePoint.ToString("HH:mm");
                    var historyData = db.Queryable<Entities.Models.EMS.EQPOUTPUTBYHOUR2>()
                        .Where(x => x.EQUIPMENT == equipmentId && x.NOWDAY == _nowDay && x.HOURS1 == _hour)
                        .OrderByDescending(x => x.CREATEDATE)
                        .First();

                    // 从JAPRINTQTYINFO表查询最新产量数据
                    var startTime = timePoint.AddHours(-1);
                    var printQtyData = db.Queryable<Entities.Models.Eap.PrintQtyModel.RequestInfo>()
                        .Where(x => x.Equipment == equipmentId && x.CreateDate >= startTime && x.CreateDate < now)
                        .OrderByDescending(x => x.CreateDate)
                        .First();

                    // 如果有历史数据，将其加入Redis
                    if (historyData != null)
                    {
                        int outputQty = 0;
                        if (printQtyData != null)
                        {
                            outputQty = printQtyData.OutputQty;
                        }

                        // 创建TimePointData
                        timePointData = new TimePointData
                        {
                            OutputQty = outputQty,
                            HourlyOutput = int.Parse(historyData.OUTPUT),
                            UpdateTime = _dateTimeProvider.Now
                        };

                        // 保存到Redis
                        string timePointStr = timePoint.ToString("HH:mm");
                        string timePointJson = Newtonsoft.Json.JsonConvert.SerializeObject(timePointData.ToDictionary());
                        redisClient.HashSet(redisKey, new Dictionary<string, string> { { timePointStr, timePointJson } }, TimeSpan.FromDays(7));
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"加载设备{equipmentId}历史数据到Redis出错：{ex.Message}", ex);
            }

            return timePointData;
        }

        /// <summary>
        /// 获取上一个时间点
        /// </summary>
        private string GetPreviousTimePoint(DateTime timePoint)
        {
            DateTime prevTimePoint = timePoint.AddHours(-1);
            return prevTimePoint.ToString("HH:mm");
        }

        /// <summary>
        /// 根据时间点获取班次
        /// </summary>
        private string GetShiftByTimePoint(DateTime timePoint)
        {
            int hour = timePoint.Hour;
            if (hour >= 8 && hour < 20)
            {
                return "白班";
            }
            else
            {
                return "夜班";
            }
        }

        /// <summary>
        /// 判断是否属于白班
        /// </summary>
        /// <returns></returns>
        public bool BlongDayShift()
        {
            string begintime = "07:30:00";
            string endtime = "19:29:59";
            TimeSpan bgtimespan = DateTime.Parse(begintime).TimeOfDay;
            TimeSpan endtimespan = DateTime.Parse(endtime).TimeOfDay;
            TimeSpan nowspan = _dateTimeProvider.Now.TimeOfDay;
            if (nowspan > bgtimespan && nowspan < endtimespan)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// Ng通道数量接口
        /// </summary>
        /// <param name="model"></param>
        /// <param name="datacontrol"></param>
        /// <returns></returns>
        public bool EquipmentNgQty(EquipmentNgQtyModel.RequestInfo model)
        {
            bool result = false;
            string factory = string.Empty;
            string spec = string.Empty;
            string ResourceDescription = string.Empty;
            string SubDescr = string.Empty;
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                string Equipment = Repository.GetResourceName(model.Equipment);
                if (!string.IsNullOrEmpty(Equipment))
                {
                    DataTable dt = Repository.GetSpecDescByResource(model.Equipment);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        factory = dt.Rows[0]["FACTORYNAME"].ToString() == null ? null : dt.Rows[0]["FACTORYNAME"].ToString();
                        spec = dt.Rows[0]["SPECNAME"].ToString() == null ? null : dt.Rows[0]["SPECNAME"].ToString();
                        ResourceDescription = dt.Rows[0]["DESCRIPTION"].ToString() == null ? null : dt.Rows[0]["DESCRIPTION"].ToString();
                        SubDescr = dt.Rows[0]["SUBDESCRIPTION"].ToString() == null ? null : dt.Rows[0]["SUBDESCRIPTION"].ToString();
                    }
                    result = Repository.SaveEquipmentNgQtyInfo(model, factory, spec, ResourceDescription, SubDescr);
                }
                else
                    result = false;
            }
            catch (Exception ex)
            {
                //异常处理
            }
            return result;
        }

        /// <summary>
        /// 炉管状态接口
        /// </summary>
        /// <param name="model"></param>
        /// <param name="datacontrol"></param>
        /// <returns></returns>
        public bool TubeStatus(TubeStatusModel.RequestInfo model)
        {
            bool result = false;
            string factory = string.Empty;
            string spec = string.Empty;
            string ResourceGroup = string.Empty;
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                DataTable dt = Repository.GetSpecDescByResource(model.Equipment);
                if (dt != null && dt.Rows.Count > 0)
                {
                    factory = dt.Rows[0]["FACTORYNAME"].ToString();
                    spec = dt.Rows[0]["SPECNAME"].ToString();
                    //model.Equipment = dt.Rows[0]["DESCRIPTION"].ToString();
                    ResourceGroup = dt.Rows[0]["RESOURCEGROUPNAME"].ToString();
                }
                result = Repository.SaveTubeStatus(model, factory, spec, ResourceGroup);

            }
            catch (Exception ex)
            {
                //异常处理
            }
            return result;
        }

        /// <summary>
        /// 舟使用次数接口
        /// </summary>
        /// <param name="model"></param>
        /// <param name="datacontrol"></param>
        /// <returns></returns>
        public bool BoatNumber(BoatModel.RequestInfo model)
        {
            bool result = false;
            string factory = string.Empty;
            string spec = string.Empty;
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            BoatRepository boatRepository = new BoatRepository();
            try
            {

                result = Repository.SaveBoatNumber(model);
            }
            catch (Exception ex)
            {
                //异常处理
            }
            return result;
        }

        /// <summary>
        /// 舟使用次数接口
        /// </summary>
        /// <param name="model"></param>
        /// <param name="datacontrol"></param>
        /// <returns></returns>
        public bool EquBoatQty(EquBoatModel.RequestInfo model)
        {
            bool result = false;
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                result = Repository.SaveEquBoatQty(model);
            }
            catch (Exception ex)
            {
                //异常处理
                throw;
            }
            return result;
        }


        public string ALDPuttyInfo(List<ALDPuttyModel.RequestInfo> model)
        {
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                Repository.SaveALDPuttyInfo(model);
            }
            catch (Exception e)
            {
                return e.Message;
            }
            return string.Empty;
        }


        public string LIFCheckPointTable(List<LIFCheckPointModel.RequestInfo> model)
        {
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                Repository.SaveLIFCheckPointTable(model);
            }
            catch (Exception e)
            {
                return e.Message;
            }
            return string.Empty;
        }


        public string EapTubeAutoBoatInfo(EapTubeAutoBoatInfoModel.RequestInfo model)
        {
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                Repository.SaveEapTubeAutoBoatInfo(model);
            }
            catch (Exception e)
            {
                return e.Message;
            }
            return string.Empty;
        }


        public string LPRaggedFicheInfo(LPRaggedFicheModel.RequestInfo model)
        {
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                Repository.SaveLPRaggedFicheInfo(model);
            }
            catch (Exception e)
            {
                return e.Message;
            }
            return string.Empty;
        }


        public async Task<string> FlowerBasketCardInfo(FlowerBasketCardInfoModel.RequestInfo model)
        {
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                await Repository.SaveFlowerBasketCardInfo(model);
            }
            catch (Exception e)
            {
                return e.Message;
            }
            return string.Empty;
        }

        public async Task<string> FlowerBasketBoatCardInfo(FlowerBasketBoatCardModel.RequestInfo model)
        {
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                await Repository.SaveFlowerBasketBoatCardInfo(model);
            }
            catch (Exception e)
            {
                return e.Message;
            }
            return string.Empty;
        }

        /// <summary>
        /// 报警信息接口
        /// </summary>
        /// <param name="model"></param>
        /// <param name="datacontrol"></param>
        /// <returns></returns>
        public bool EapAlarm(AlarmModel.RequestInfo model)
        {
            bool result = false;
            string factory = string.Empty;
            string spec = string.Empty;
            string description = string.Empty;
            string resourceGroupName = string.Empty;
            string resourceGroupId = string.Empty;
            string resourceName = string.Empty;

            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                DataTable dt = Repository.GetSpecDescByResource(model.Equipment);
                if (dt != null && dt.Rows.Count > 0)
                {
                    factory = dt.Rows[0]["FACTORYNAME"].ToString();
                    spec = dt.Rows[0]["SPECNAME"].ToString();
                    description = dt.Rows[0]["SUBDESCRIPTION"].ToString();
                    resourceGroupName = dt.Rows[0]["resourcefamilyname"].ToString();
                    resourceName = dt.Rows[0]["RESOURCENAME"].ToString();

                }
                result = Repository.SaveEapAlarm(model, factory, spec, description, resourceGroupName, resourceName);
            }
            catch (Exception ex)
            {
                //异常处理
            }
            return result;
        }

        /// <summary>
        /// 报警信息接口
        /// </summary>
        /// <param name="model"></param>
        /// <param name="datacontrol"></param>
        /// <returns></returns>
        public bool EapAldAlarm(AldAlarmModel.RequestInfo model)
        {
            bool result = false;
            string factory = string.Empty;
            string spec = string.Empty;
            string description = string.Empty;
            string resourceGroupName = string.Empty;
            string resourceGroupId = string.Empty;

            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                DataTable dt = Repository.GetSpecDescByResource(model.Equipment);
                if (dt != null && dt.Rows.Count > 0)
                {
                    factory = dt.Rows[0]["FACTORYNAME"].ToString();
                    spec = dt.Rows[0]["SPECNAME"].ToString();
                    description = dt.Rows[0]["SUBDESCRIPTION"].ToString();
                    resourceGroupName = dt.Rows[0]["resourcefamilyname"].ToString();

                }
                result = Repository.SaveEapAldAlarm(model, factory, spec, description, resourceGroupName);
            }
            catch (Exception ex)
            {
                //异常处理
            }
            return result;
        }

        /// <summary>
        /// 花篮清洗接口数据从EAP->MES
        /// </summary>
        /// <param name="model"></param>
        /// <param name="datacontrol"></param>
        /// <returns></returns>
        public bool SaveCarrierClean(CarrierCleanModel.RequestInfo model)
        {
            bool result = false;
            string factory = string.Empty;
            string spec = string.Empty;
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                DataTable dt = Repository.GetSpecDescByResource(model.Equipment);
                if (dt != null && dt.Rows.Count > 0)
                {
                    factory = dt.Rows[0]["FACTORYNAME"].ToString();
                    spec = dt.Rows[0]["SPECNAME"].ToString();
                }
                result = Repository.SaveCarrierCleanInfo(model, factory, spec);

            }
            catch (Exception ex)
            {
                //异常处理
            }
            return result;
        }

        public bool EquipmentStatus(EquipmentStatusModel.RequestInfo model)
        {
            bool result = false;
            string factory = string.Empty;
            string spec = string.Empty;
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                DataTable dt = Repository.GetSpecDescByResource(model.Equipment);
                if (dt != null && dt.Rows.Count > 0)
                {
                    factory = dt.Rows[0]["FACTORYNAME"].ToString();
                    spec = dt.Rows[0]["SPECNAME"].ToString();
                }
                result = Repository.SaveEquipmentStatusInfo(model, factory, spec);

            }
            catch (Exception ex)
            {
                //异常处理
            }
            return result;
        }


        private static ConcurrentDictionary<string, Dictionary<string, object>> _eqpStatusBuffer = new ConcurrentDictionary<string, Dictionary<string, object>>();


        /// <summary>
        /// 记录设备状态
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public string EQPStatus(EQPStatusModel.RequestInfo model)
        {
            try
            {
                #region 参数判断

                //判断设备号 Equipment 是否存在
                if (string.IsNullOrEmpty(model.Equipment))
                {
                    return "设备号不能为空";
                }

                if (string.IsNullOrEmpty(model.ID))
                {
                    return "设备状态 ID不能为空";
                }
                #endregion

                //说明具备 Equipment 和 Status ID 两个值了，判断当前值，是否在相关表被定义。
                //Equipment 查找 resourcedef 表；Status 查找 resourcestatuscode
                //判断设备状态 ID 是否存在

                EapInterfaceRepository repository = new EapInterfaceRepository();
                var statusResTableRows = repository.getResourcestatuscode(model.ID);
                if (statusResTableRows.Length == 0)
                {
                    return $"不存在ID为{model.ID}的设备状态。";
                }
                string Status = statusResTableRows[0]["DESCRIPTION"].ToString();
                string ChangeStatus = statusResTableRows[0]["CHANGESTATUS"].ToString();

                //根据 设备 查询 工序描述
                DataTable dt = repository.GetSpecDescByResource2(model.Equipment);
                if (dt != null && dt.Rows.Count == 0)
                {
                    return $"MES设备:{model.Equipment}查询无工序信息，请及时维护";
                }

                string spec = dt.Rows[0]["SPECNAME"].ToString();                     //工序
                string factory = dt.Rows[0]["FACTORYNAME"].ToString();               //工厂
                string decription = dt.Rows[0]["DESCRIPTION"].ToString();             //设备机台号
                //string resourcegroupname = dt.Rows[0]["resourcegroupname"].ToString();
                //string resourcegroupid = dt.Rows[0]["resourcegroupid"].ToString();
                string resourceId = dt.Rows[0]["resourceid"].ToString();              //resourceid
                string parentResourceId = dt.Rows[0]["parentresourceid"].ToString();  //parentresourceid
                string equipment = dt.Rows[0]["equipment"].ToString();             //设备ID

                string newStatus = string.IsNullOrEmpty(ChangeStatus) ? Status //假如changgeStatus 为空，则新状态就是Status
                                                                      : ChangeStatus; //假如changgeStatus 不为空，则新状态就是ChangeStatus

                string nearStatus = string.Empty;
                string startTime = string.Empty;
                string endTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");


                string code = StringHelper.Equals(newStatus, "设备故障") ? "0" : "1";

                //数据库新一行的GUID
                string newID = Guid.NewGuid().ToString();
                //保存设备状态
                bool result = repository.SaveEQPStatusInfo(equipment, model.ID, Status, ChangeStatus, factory, spec, decription, code, endTime, newID, resourceId, parentResourceId);
                if (result)//成功向中间库 jaEQPStatus表插入新的设备状态
                {
                    //向EAM 发送故障工单
                    eamReportTask(model.Equipment, newStatus, code, endTime, newID);

                    //if (near.Rows.Count > 0)
                    //{
                    //    EqpStatusDuration eqpStatusDuration = new EqpStatusDuration(equipment, decription, startTime, endTime, nearStatus, newStatus, resourceId, parentResourceId);
                    //    eqpStatusDuration.SaveDuration();
                    //}
                    return null;
                }
                else
                {
                    return "记录了0行数据";
                }

            }
            catch (Exception e)
            {
                LogHelper.Error(e.Message, e);
                return e.Message;
            }
        }

        /// <summary>
        /// 设备状态提交任务
        /// </summary>
        /// <param name="outEquipment"></param>
        /// <param name="outChangeStatus"></param>
        /// <param name="outNewStatus"></param>//string outChangeStatus, string outStatus,
        private void eamReportTask(string outEquipment, string outNewStatus, string outCode, string outCreateDate, string outID)
        {
            if ("true" == ConfigReader.UseEamConnection.ToLower())
            {
                Task.Run(() =>
                {

                    //DateTime startRun = DateTime.Now;
                    try
                    {
                        #region 向EAM提交设备状态

                        ReportFailToEAMModel data = new ReportFailToEAMModel
                        {
                            Code = outCode, //设备故障 触发故障工单, 0 触发 : 1 不触发
                            Equipment = outEquipment,
                            Status = outNewStatus
                        };

                        ReportFailToEAMModelFromTable model = new ReportFailToEAMModelFromTable();
                        model.Data = data;
                        model.Data.CreateDate = outCreateDate;
                        model.Data.TriggerDate = outCreateDate;
                        model.ID = outID;

                        ESBRequestModel<ReportFailToEAMModel> eSBRequest = new ESBRequestModel<ReportFailToEAMModel>();
                        eSBRequest.esbInfo = new ESBRequestModelBase.EsbInfo() { instId = Guid.NewGuid().ToString() };
                        PostReportToEAM(eSBRequest, model, "api/EapInterface/jaEquipmentStatus");

                        #endregion

                    }
                    catch (Exception e)
                    {
                        Infrastructure.Utilities.LogHelper.Error(e.Message, e);
                    }
                    //finally
                    //{
                    //    LogHelper.Info($"eamReportTask run time: {(DateTime.Now - startRun).TotalMilliseconds} ms");
                    //}
                });
            }

        }

        /// <summary>
        /// 向EAM 发送设备状态信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public void PostReportToEAM(ESBRequestModelBase requestData, ReportFailToEAMModelFromTable model, string moduleName)
        {
            if ("true" == ConfigReader.UseEamConnection.ToLower())
            {
                try
                {
                    #region 创建数据传送计划

                    ESBRequestModel<ReportFailToEAMModel> reportContent = new ESBRequestModel<ReportFailToEAMModel>() { requestInfo = model.Data };
                    reportContent.esbInfo = requestData.esbInfo;

                    Plan<PostPlanModel<ESBRequestModel<ReportFailToEAMModel>>>.Run( //运行计划
                        new PostPlanModel<ESBRequestModel<ReportFailToEAMModel>>(reportContent), (param) =>
                        {
                            return reportFailAction(param);
                        });

                    #endregion

                }
                catch (Exception e)
                {
                    Infrastructure.Utilities.LogHelper.Error(e.Message, e);
                }
            }
        }

        private static bool reportFailAction(PostPlanModel<ESBRequestModel<ReportFailToEAMModel>> param)
        {
            if (DateTime.Now <= param.TriggerDate)
                return false;//继续等

            try
            {
                var eamService = new EAMService<ESBRequestModel<ReportFailToEAMModel>>();

                string url = ConfigReader.EamRootPath + "mem/changestatus/";
                string maxauth = ConfigReader.EamMAXAUTH;

                Dictionary<string, string> headers = new Dictionary<string, string>() { { "MAXAUTH", maxauth } };
                param.DataContent.esbInfo.requestTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.FFF");
                var result = eamService.PostJson(url, param.DataContent, headers).Result;

                //回传参数发生了点问题30s后，再发送一次,只重复2次
                param.TriggerDate = param.NextTriggerDate();
                param.ErrMessage = result.Value;
                param.LogID = Guid.NewGuid().ToString();

                param.RepeatNum++;
                if (result.Key != 0 && param.RepeatNum < 4)
                {
                    //回传参数发生了点问题, 30s后，再发送一次,只重复2次
                    //repository.NewJaPostLog(param);
                    return false;
                }
                else if (result.Key == 0 && param.RepeatNum < 4)
                {
                    EAMSubmitResultInfo body = Newtonsoft.Json.JsonConvert.DeserializeObject<EAMSubmitResultInfo>(result.Value);
                    if (body.esbInfo.returnCode != "200")
                    {
                        //回传参数发生了点问题, 30s后，再发送一次,只重复2次
                        //repository.NewJaPostLog(param);
                        return false;
                    }
                }
                //返回参数成功，或重复提交次数超过2次
            }
            catch (Exception e)
            {

                Infrastructure.Utilities.LogHelper.Error(e.Message, e);
            }
            return true;
        }


        public static bool jaZRJPCarrierOut(jaZRJPCarrierOutModel data)
        {
            if (string.IsNullOrEmpty(data.slot))
            {
                throw new Exception("槽号slot 缺少值。");
            }

            Dictionary<string, string> row = new Dictionary<string, string>() {
                { "GUID",$"'{Guid.NewGuid().ToString()}'" },
                { "Equipment",$"'{data.Equipment}'" },
                { "slot",$"'{data.slot}'" },
                { "cstId1","''" },
                { "cstId2","''" },
                { "cstId3","''" },
                { "cstId4","''" },
                { "cstId5","''" },
                { "cstId6","''" },
                { "CREATEDATE", $"to_date('{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}','yyyy-mm-dd hh24:mi:ss')" },
            };

            foreach (var carrier in data.Carrierlist)
            {
                row[carrier.ItemName] = $"'{carrier.Value}'";
            }

            EapInterfaceRepository repository = new EapInterfaceRepository();
            return repository.jaZRJPCarrierOut(row);
        }

        /// <summary>
        /// 在线检测接口
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public bool SaveWaferCheck(WaferCheckModel.RequestInfo model)
        {
            bool result = false;
            string factory = string.Empty;
            string spec = string.Empty;
            string eqpDescription = string.Empty;
            string tablename = string.Empty;
            string detailtable = string.Empty;
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                DataTable dt = Repository.GetSpecDescByResource(model.Equipment);
                if (dt != null && dt.Rows.Count > 0)
                {
                    factory = dt.Rows[0]["FACTORYNAME"].ToString();
                    spec = dt.Rows[0]["SPECNAME"].ToString();
                    eqpDescription = dt.Rows[0]["DESCRIPTION"].ToString();

                    model.Factory = factory;
                    model.Spec = spec;
                    model.EqpDescription = eqpDescription;
                }
                switch (model.ActionCode)
                {
                    case "KSFZ": //扩散在线方阻
                        tablename = "jaWaferCheck";
                        detailtable = "jaWaferCheckParameters";
                        result = Repository.SaveWaferCheck1(model, tablename, detailtable);
                        break;
                    case "SEJG": //SE激光在线检测数据
                        tablename = "JASEJGCheck";
                        detailtable = "jaSEJGparameters";
                        result = Repository.SaveWaferCheck(model, tablename, detailtable);
                        break;
                    case "ZRHL": //制绒下料花篮检测
                        tablename = "JAZRHLCheck";
                        detailtable = "jaZRHLparameters";
                        result = Repository.SaveWaferCheck(model, tablename, detailtable);
                        break;
                    case "JPHL": //碱抛下料花篮检测
                        tablename = "JAJPHLCheck";
                        detailtable = "jaJPHLparameters";
                        result = Repository.SaveWaferCheck(model, tablename, detailtable);
                        break;
                    case "ZMAOI": //正膜AOI

                        tablename = "ZMAOIinspection";
                        result = Repository.SaveWaferCheck(model, tablename, detailtable);
                        break;
                    case "BMAOI": //背膜AOI

                        tablename = "BMAOIinspection";
                        result = Repository.SaveWaferCheck(model, tablename, detailtable);
                        break;
                    case "ZMPL": //正膜PL检测
                        tablename = "ZMPLinspection";
                        result = Repository.SaveWaferCheck(model, tablename, detailtable);
                        break;
                    case "BMPL": //背膜PL检测
                        tablename = "BMPLinspection";
                        result = Repository.SaveWaferCheck(model, tablename, detailtable);
                        break;
                    case "YSZMAOI": //印刷正面AOI检测
                        tablename = "YSZMAOIinspection";
                        result = Repository.SaveWaferCheck(model, tablename, detailtable);
                        break;
                    case "YSBMAOI": //印刷背面AOI检测
                        //tablename = "JAYSBMAOICheck";
                        //detailtable = "jaYSBMAOIparameters";
                        tablename = "YSBMAOIinspection";
                        result = Repository.SaveWaferCheck(model, tablename, detailtable);
                        break;
                    case "BZZMAOI": //包装正面AOI检测
                        //tablename = "JABZZMAOICheck";
                        //detailtable = "jaBZZMAOIparameters";
                        tablename = "BZZMAOIinspection";
                        result = Repository.SaveWaferCheck(model, tablename, detailtable);
                        break;
                    case "BZBMAOI": //包装背面AOI检测
                        //tablename = "JABZBMAOICheck";
                        //detailtable = "jaBZBMAOIparameters";
                        tablename = "BZBMAOIinspection";
                        result = Repository.SaveWaferCheck(model, tablename, detailtable);
                        break;
                    case "IVEL": //IV测试
                        //tablename = "JAIVCheck";
                        //detailtable = "jaIVparameters";
                        tablename = "JAIVTESTDATA";
                        result = Repository.SaveWaferCheck(model, tablename, detailtable);
                        break;

                    case "ZRJPCZ": //制绒碱抛减重

                        result = Repository.SaveWaferCheck_ZRJPCZ(model);
                        break;
                    //case "EL": //EL测试
                    //    tablename = "JAELCheck";
                    //    detailtable = "jaELparameters";
                    //    result = Repository.SaveWaferCheck(model, tablename, detailtable);
                    //    break;
                    case "ZRAOI": //制绒检测
                        tablename = "ZRAOIinspection";
                        detailtable = "ZRAOIparameters";
                        result = Repository.SaveWaferCheck(model, tablename, detailtable);
                        break;
                    case "BSGAOI": //去BSG检测
                        tablename = "BSGAOIinspection";
                        detailtable = "BSGAOIparameters";
                        result = Repository.SaveWaferCheck(model, tablename, detailtable);
                        break;
                    case "PSGAOI": //去PSG检测
                        tablename = "PSGAOIinspection";
                        detailtable = "PSGAOIparameters";
                        result = Repository.SaveWaferCheck(model, tablename, detailtable);
                        break;
                    case "ALDAOI": //ALD检测
                        tablename = "ALDAOIinspection";
                        detailtable = "ALDAOIparameters";
                        result = Repository.SaveWaferCheck(model, tablename, detailtable);
                        break;

                    //制绒检测ZRAOI、去BSG检测: BSGAOI、去PSG检测: PSGAOI、ALD检测：ALDAOI
                    default:
                        throw new Exception("请检查ActionCode:" + model.ActionCode);
                }
            }
            catch (Exception ex)
            {
                throw;
            }
            return result;
        }


        /// <summary>
        /// 保存设备主数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public bool EqpMainInfo(EqpMainInfoModel model)
        {
            //定义存储层的工具类
            EamInterfaceRepository repository = new EamInterfaceRepository();

            return repository.SaveEqpMainInfo(model);
        }

        /// <summary>
        /// 保存设备参数
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<KeyValuePair<int, string>> SaveEqpParam(EqpParamModel model)
        {
            try
            {
                EapInterfaceRepository repository = new EapInterfaceRepository();

                //判断设备是否在MES定义
                if (!repository.IsEquipmentExist(model.Equipment))
                {
                    return new KeyValuePair<int, string>(1, "查无此设备");
                }

                //更新"设备参数更新表"
                // Models.EqpParamLastValue.Set(model.ActionCode, model.Equipment, model.ParameterList);

                //获取需要保存的设备参数
                Dictionary<string, string> eqpParams = repository.GetParamLib(model.ActionCode);
                if (eqpParams == null)
                {
                    return new KeyValuePair<int, string>(1, "查无此工序对应的参数列表");
                }

                //过滤从EAP传过来的参数，获取当前接口需要保存的参数
                List<EqpParamModel.EqpParamListItem> saveParams = filtEqpParams(eqpParams, model.ParameterList);
                if (saveParams.Count == 0)
                {
                    return new KeyValuePair<int, string>(1, "工序参数拒绝导入");
                }

                //获取设备参数字段值
                model = setDefaultValue(model);

                //检查并调整特定参数值
                model = checkAndAdjustParamValues(model, saveParams);

                //保存设备参数
                return await repository.SaveEqpParam(model, saveParams);
            }
            catch (Exception e)
            {
                LogHelper.Error(e.Message, e);
                return new KeyValuePair<int, string>(-1, e.Message);
            }
        }

        /// <summary>
        /// 获取管式设备工艺类型 参数名
        /// </summary>
        /// <param name="model"></param>
        /// <param name="name"></param>
        /// <returns></returns>
        private IEnumerable<EqpParamModel.EqpParamListItem> getDefaultValue(EqpParamModel model, params string[] name)
        {
            EqpParamModel.EqpParamListItem item = null;
            item = model.ParameterList.Find((x) =>
            {
                if (x.name != null)
                {
                    bool rlt = x.name.ToLower().Contains(name[0].ToLower());
                    if (rlt)
                    {
                        model.Tube = x.name.ToLower().Replace("tube", "").Replace(name[0].ToLower(), "");
                    }
                    return rlt;
                }
                return false;
            });
            yield return item;

            item = model.ParameterList.Find((x) =>
            {
                if (x.name != null)
                {
                    return x.name.ToLower().Contains(name[1].ToLower());
                }
                return false;
            });

            yield return item;
        }

        /// <summary>
        /// 设置管式设备工艺类型 参数名
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private EqpParamModel setDefaultValue(EqpParamModel model)
        {
            Action<int, string> setValue = (index, value) =>
            {
                switch (index)
                {
                    case 0: model.RecipeID = value; break;
                    case 1: model.Stepid = value; break;
                }
            };

            Type type = typeof(EqpParamType);
            Dictionary<string, Action> filter = new Dictionary<string, Action>()
            {
#region KS 扩散

                {   Enum.GetName(type, EqpParamType.KS), ()=>{
                    int index = 0;
                    foreach(var item in getDefaultValue(model,"RecipeId","StepId" ))
                    {
                        if(item != null) setValue(index, item.value);
                        index++;
                    }
                } },
#endregion
#region BMDM 背膜

                {   Enum.GetName(type, EqpParamType.BMDM), ()=>{
                    int index = 0;
                    foreach(var item in getDefaultValue(model,"DataVarRecipe","CurrStep" ))
                    {
                        if(item != null) setValue(index, item.value);
                        index++;
                    }
                } },
#endregion

#region YH 氧化

                {   Enum.GetName(type, EqpParamType.YH), ()=>{
                    int index = 0;
                    foreach(var item in getDefaultValue(model,"DataVarRecipe","CurrStep" ))
                    {
                        if(item != null) setValue(index, item.value);
                        index++;

                    }

                } },
#endregion
#region ZMDM 正膜

                {   Enum.GetName(type, EqpParamType.ZMDM), ()=>{
                    int index = 0;
                    foreach(var item in getDefaultValue(model,"ProcessStepType","ProcessStepid" ))
                    {
                        if(item != null) setValue(index, item.value);
                        index++;
                    }
                } }
#endregion
            };

            if (filter.ContainsKey(model.ActionCode))
            {
                filter[model.ActionCode]();
            }
            return model;
        }

        /// <summary>
        /// 过滤从EAP传过来的参数，获取当前接口需要保存的参数
        /// </summary>
        /// <param name="eqpParams"></param>
        /// <param name="ParameterList"></param>
        /// <returns></returns>
        private List<EqpParamModel.EqpParamListItem> filtEqpParams(Dictionary<string, string> eqpParams, List<EqpParamModel.EqpParamListItem> ParameterList)
        {

            List<EqpParamModel.EqpParamListItem> saveParams = new List<EqpParamModel.EqpParamListItem>();
            foreach (var param in ParameterList)
            {
                //这里需要加上参数配置项的校验，看看参数是否合法
                if (eqpParams.ContainsKey(param.name))
                {
                    saveParams.Add(param);
                }
            }

            return saveParams;

        }

        /// <summary>
        /// 启动一个设备参数自动采集任务，返回采集到的数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public void EqpAutoCollectParam(ESBRequestModelBase requestData, EqpCollectedParamModel model)
        {
            if ("true" == ConfigReader.UseEamConnection.ToLower())
            {
                Task.Run(async () =>
                {
                    try
                    {
                        EapInterfaceRepository repository = new EapInterfaceRepository();

                        //从数据库中间库采集参数
                        DataTable collectedResult = await repository.EqpAutoCollectParam(model);
                        int collectionCount = 0;
                        if (collectedResult != null)
                        {
                            foreach (DataRow row in collectedResult.Rows)
                            {
                                var equipment = row["Equipment"].ToString();
                                var asset = model.Assets.Find(x => StringHelper.Equals(x.AssetNum, equipment));
                                if (asset == null) continue;

                                var name = row["ParamName"].ToString();
                                var line = asset.Lines.Find(x => StringHelper.Equals(x.CheckMethod, name));
                                if (line == null) continue;

                                line.CheckValue = row["ParamValue"].ToString();
                                collectionCount++;
                            }
                        }

                        Infrastructure.Utilities.LogHelper.Info("从数据库中间库采集到参数，行数：" + collectionCount);

                        #region 创建数据传送计划
                        ESBRequestModel<EqpCollectedParamModel> reportContent = new ESBRequestModel<EqpCollectedParamModel>() { requestInfo = model };
                        reportContent.esbInfo = requestData.esbInfo;
                        Plan<PostPlanModel<ESBRequestModel<EqpCollectedParamModel>>>.Run( //运行计划
                           new PostPlanModel<ESBRequestModel<EqpCollectedParamModel>>(reportContent), (param) =>
                           {
                               return postEqpAutoCollectParam(param, repository);
                           });

                        #endregion
                    }
                    catch (Exception e)
                    {
                        Infrastructure.Utilities.LogHelper.Error(e.Message, e);
                    }

                });
            }
        }


        private static bool postEqpAutoCollectParam(PostPlanModel<ESBRequestModel<EqpCollectedParamModel>> param, EapInterfaceRepository repository)
        {
            if (DateTime.Now <= param.TriggerDate)
                return false;//继续等

            try
            {
                var eamService = new EAMService<ESBRequestModel<EqpCollectedParamModel>>();

                string url = ConfigReader.EamRootPath + "mem/autocheck/";
                string maxauth = ConfigReader.EamMAXAUTH;

                Dictionary<string, string> headers = new Dictionary<string, string>() { { "MAXAUTH", maxauth } };
                param.DataContent.esbInfo.requestTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.FFF");
                var result = eamService.PostJson(url, param.DataContent, headers).Result;

                //回传参数发生了点问题30s后，再发送一次,只重复2次
                param.TriggerDate = param.NextTriggerDate();
                param.ErrMessage = result.Value;
                param.LogID = Guid.NewGuid().ToString();

                param.RepeatNum++;
                if (result.Key != 0 && param.RepeatNum < 4)
                {
                    //回传参数发生了点问题, 30s后，再发送一次,只重复2次
                    ////repository.NewJaPostLog(param);
                    return false;
                }
                else if (result.Key == 0 && param.RepeatNum < 4)
                {
                    EAMSubmitResultInfo body = Newtonsoft.Json.JsonConvert.DeserializeObject<EAMSubmitResultInfo>(result.Value);
                    if (body.esbInfo.returnCode != "200")
                    {
                        //回传参数发生了点问题, 30s后，再发送一次,只重复2次
                        Infrastructure.Utilities.LogHelper.Error(body.esbInfo.returnCode + "错误，接口/mem/autocheck/ 返回:" + body.esbInfo.returnMsg);
                        //repository.NewJaPostLog(param);
                        return false;
                    }
                }
                //返回参数成功，或重复提交次数超过2次
            }
            catch
            {

            }
            return true;
        }



        public List<string> GetEqpList(string dbtbName, string condition, string target)
        {
            List<string> EqpList = new List<string>();
            EapInterfaceRepository repository = new EapInterfaceRepository();
            //DataTable Eqpdt = repository.GetEqpdt(dbtbName, condition,target);
            //if (Eqpdt.Rows.Count<0)
            //{
            //    foreach (DataRow item in Eqpdt.Rows)
            //    {
            //        EqpList.Add(item[0].ToString());
            //    }
            //}
            return EqpList;
        }
        public ResultStatus InsertEQParameter(EapParameterModel.RequestInfo data)
        {
            ResultStatus oResult = new ResultStatus();
            oResult.ResultCode = "0";
            string strGuid = Guid.NewGuid().ToString();

            EapInterfaceRepository repository = new EapInterfaceRepository();
            try
            {
                //if (data.Equipment.IsNullOrEmpty() || data.ActionCode.IsNullOrEmpty())
                //{
                //    msg.Append($"【设备编号】和【工序】不得为空\n");
                //    return msg.ToString();
                //}
                //获取specname、description、factoryname/工序名称，设备机台，车间名称
                //DataTable dtInfo = repository.GetResourceInfoByEquipment(data.Equipment);
                ////获取需要采集的参数点
                //DataTable eqpParamLibraryTable = repository.GetIsNeedCollect(data.ActionCode);
                //if (eqpParamLibraryTable.Rows.Count < 1)
                //{
                //    msg.Append($"没有需要被采集的参数数据1\n");
                //    return msg.ToString(); ;
                //}

                #region 存入【jaEQParameterinfo】主表数据
                oResult = repository.InsertjaEQParameterinfo(strGuid, data);
                #endregion

                /*
                #region 存入参数变更JAPARAMCHANGEPARAMETERS表数据

                //找到需要被采集的参数名称后制作要插入的List<Dictionary<string, string>>
                List<Dictionary<string, string>> dichangeList = new List<Dictionary<string, string>>();//JAparamchange
                //guid
                //获取参数表（内包含旧参数）
                DataTable oldParamTable = repository.GetoldParam(data.Equipment, data.RecipeName);

                //判定有变更的参数
                if (oldParamTable != null && oldParamTable.Rows.Count > 0)
                {
                    for (int i = 0; i < oldParamTable.Rows.Count; i++)
                    {
                        string oldName = oldParamTable.Rows[i]["NAME"].ToString(); //旧数据参数名
                        string oldValue = oldParamTable.Rows[i]["VALUE"].ToString(); //旧数据参数值
                        foreach (var lstParm in data.ParmTableN)
                        {
                            if (oldName == lstParm.name && oldValue != lstParm.value)
                            {
                                Dictionary<string, string> dic = new Dictionary<string, string>()
                          {
                             { "GUID",strGuid},
                             { "NAME",lstParm.name},
                             { "VALUE",oldParamTable.Rows[i]["VALUE"].ToString()},
                             { "CREATEDATE",DateTime.Now.ToString()},
                             { "ACTIONCODE",data.ActionCode},
                             { "CHANGEVALUE",lstParm.value},
                             { "STEPNO","" }
                          };
                                dichangeList.Add(dic);
                            }
                        }
                    }
                    oResult = repository.InsertjaEQParameterChange(dichangeList);
                }
                #endregion
                */

                #region 存入jaEQParameterDetail 明细表数据
                //foreach (var lstParm in data.ParmTableN)
                //{
                //    Dictionary<string, string> dic = new Dictionary<string, string>()
                //    {
                //        {"GUID",strGuid},
                //        {"NAME",lstParm.name},
                //        {"VALUE",lstParm.value},
                //        {"CREATEDATE",DateTime.Now.ToString()},
                //        {"ACTIONCODE",data.ActionCode}
                //    };
                //    dicdetailList.Add(dic);
                //}
                oResult = repository.InsertjaEQParameterDetail(strGuid, data);
                #endregion

                //if (oResult.ResultCode == "1") //参数发生变更发送邮件预警
                //{
                //    //构造预警邮件格式
                //    string strMailSubject = string.Empty;
                //    strMailSubject = "主机台参数变更预警";
                //    string sMailBody = string.Empty;
                //    string sQueryCondition = string.Empty;

                //    sQueryCondition = "你好，以下是主机台参数变更详细信息： ";
                //    DataTable dtMailSend = repository.QueryChangeData(strGuid);//查询参数变更数据
                //    if (dtMailSend != null && dtMailSend.Rows.Count > 0)
                //    {
                //        AlarmRepository Alarmrepository = new AlarmRepository();
                //        DataTable dtGroup = Alarmrepository.GetEmailGroup(ParasChangeEmailGroup);
                //        if (dtGroup != null && dtGroup.Rows.Count > 0)
                //        {
                //            EmailCenter.GetYield(strMailSubject, dtGroup, dtMailSend, sQueryCondition);
                //            Thread.Sleep(1000);
                //        }
                //    }
                //}
            }
            catch (Exception e)
            {
                oResult.ResultCode = "0";
                oResult.ResultMsg = e.Message;
            }

            return oResult;
        }

        public ResultStatus InsertKSZMEQParameter(KSZMEQParmModel.RequestInfo data)
        {
            ResultStatus oResult = new ResultStatus();
            oResult.ResultCode = "0";
            string strGuid = Guid.NewGuid().ToString();

            EapInterfaceRepository repository = new EapInterfaceRepository();
            try
            {
                //if (data.Equipment.IsNullOrEmpty() || data.ActionCode.IsNullOrEmpty())
                //{
                //    msg.Append($"【设备编号】和【工序】不得为空\n");
                //    return msg.ToString();
                //}
                //获取specname、description、factoryname/工序名称，设备机台，车间名称
                //DataTable dtInfo = repository.GetResourceInfoByEquipment(data.Equipment);
                ////获取需要采集的参数点
                //DataTable eqpParamLibraryTable = repository.GetIsNeedCollect(data.ActionCode);
                //if (eqpParamLibraryTable.Rows.Count < 1)
                //{
                //    msg.Append($"没有需要被采集的参数数据1\n");
                //    return msg.ToString(); ;
                //}

                #region 存入【jaEQParameterinfo】主表数据
                oResult = repository.InsertjaKSZMEQParameterinfo(strGuid, data);
                #endregion

                //guid
                //获取参数表（内包含旧参数）
               // DataTable oldParamTable = repository.GetoldParam(data.Equipment, data.Recipe, data.Tube);


                #region 存入jaEQParameterDetail 明细表数据
                string name = string.Empty;
                string value = string.Empty;
                string StepNo = string.Empty;
                List<Dictionary<string, string>> divalueList = new List<Dictionary<string, string>>();
                for (int K = 0; K < data.StepParamValues.Count; K++)
                {
                    for (int j = 0; j < data.StepParamDesc.Count; j++)
                    {
                        StepNo = data.StepParamValues[K].StepNo.ToString();
                        name = data.StepParamDesc[j].ToString();
                        value = string.IsNullOrWhiteSpace(data.StepParamValues[K].StepParamValue[j]) ? "" : data.StepParamValues[K].StepParamValue[j].ToString();
                        Dictionary<string, string> dic = new Dictionary<string, string>()
                          {
                             {"GUID",strGuid},
                             { "NAME",name},
                             { "VALUE",value},
                             {"CREATEDATE",DateTime.Now.ToString()},
                             {"ACTIONCODE",data.ActionCode},
                             {"STEPNO",StepNo}
                          };
                        divalueList.Add(dic);
                    }
                }

                //保存明细表数据
                oResult = repository.InsertKSZMEQParameterDetail(divalueList);

                //Dictionary<string, string> dic1 = new Dictionary<string, string>()
                //          {
                //             {"GUID",strGuid},
                //             { "NAME",data.StepParamValues[0].StepNo.ToString()},
                //             { "VALUE",data.StepParamValues[0].StepNo.ToString()},
                //             {"CREATEDATE",DateTime.Now.ToString()},
                //             {"ACTIONCODE",data.ActionCode}
                //          };
                //divalueList.Add(dic1);
                //oResult = repository.InsertKSZMEQParameterDetail(divalueList);
                #endregion

                /*
                #region 存入参数变更jaEQParameterChange表数据
                //找到需要被采集的参数名称后制作要插入的List<Dictionary<string, string>>
                List<Dictionary<string, string>> dichangeList = new List<Dictionary<string, string>>();

                //判定有变更的参数
                if (oldParamTable != null && oldParamTable.Rows.Count > 0)
                {
                    for (int i = 0; i < oldParamTable.Rows.Count; i++)
                    {
                        string oldName = oldParamTable.Rows[i]["NAME"].ToString(); //旧数据参数名
                        string oldValue = oldParamTable.Rows[i]["VALUE"].ToString(); //旧数据参数值
                        string step = oldParamTable.Rows[i]["STEPNO"].ToString(); //旧数据参数
                        foreach (var dic in divalueList)
                        {
                            name = dic["NAME"];
                            value = dic["VALUE"];
                            StepNo = dic["STEPNO"];
                            if (oldName == name && step == StepNo && value != oldValue)
                            {
                                Dictionary<string, string> vdic = new Dictionary<string, string>()
                          {
                             { "GUID",strGuid},
                             { "NAME",name},
                             { "STEPNO",StepNo},
                             { "VALUE",oldParamTable.Rows[i]["VALUE"].ToString()},
                             { "CREATEDATE",DateTime.Now.ToString()},
                             { "ACTIONCODE",null},
                             { "CHANGEVALUE",dic["VALUE"]}
                          };
                                dichangeList.Add(vdic);
                            }
                        }
                    }
                    oResult = repository.InsertjaEQParameterChange(dichangeList);
                    #endregion

                    //if (oResult.ResultCode == "1") //参数发生变更发送邮件预警
                    //{
                    //    //构造预警邮件格式
                    //    string strMailSubject = string.Empty;
                    //    strMailSubject = "主机台参数变更预警";
                    //    string sMailBody = string.Empty;
                    //    string sQueryCondition = string.Empty;

                    //    sQueryCondition = "你好，以下是主机台参数变更详细信息： ";
                    //    DataTable dtMailSend = repository.QueryChangeData(strGuid);//查询参数变更数据
                    //    if (dtMailSend != null && dtMailSend.Rows.Count > 0)
                    //    {
                    //        AlarmRepository Alarmrepository = new AlarmRepository();
                    //        DataTable dtGroup = Alarmrepository.GetEmailGroup(ParasChangeEmailGroup);
                    //        if (dtGroup != null && dtGroup.Rows.Count > 0)
                    //        {
                    //            EmailCenter.GetYield(strMailSubject, dtGroup, dtMailSend, sQueryCondition);
                    //            Thread.Sleep(1000);
                    //        }
                    //    }
                    //}
                }
                */
            }
            catch (Exception e)
            {
                oResult.ResultCode = "0";
                oResult.ResultMsg = e.Message;
            }

            return oResult;
        }
        /// <summary>
        /// 保存高频报警数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public bool SaveRFAlarm(RFAlarmModel.RequestInfo model)
        {
            bool result = false;
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                DataTable dt = Repository.GetSpecDescByResource(model.Equipment);
                if (dt != null && dt.Rows.Count > 0)
                {
                    model.Factory = dt.Rows[0]["FACTORYNAME"].ToString();
                    model.Spec = dt.Rows[0]["SPECNAME"].ToString();
                    model.EQPNO = dt.Rows[0]["SUBDESCRIPTION"].ToString();
                    model.ResourceGroup = dt.Rows[0]["RESOURCEGROUPNAME"].ToString();
                    //resourceGroupId = dt.Rows[0]["RESOURCEGROUPID"].ToString();
                }
                result = Repository.SaveRFAlarm(model);
            }
            catch (Exception ex)
            {
                //异常处理
            }
            return result;
        }


        public bool Electricconsumption(ElectricModel.RequestInfo model)
        {
            bool result = false;

            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                DataTable dt = Repository.GetSpecByEquipment(model.Equipment);
                string FACTORYNAME = string.Empty;
                string DESCRIPTION = string.Empty;
                string SPECNAME = string.Empty;
                if (dt != null && dt.Rows.Count > 0)
                {
                    FACTORYNAME = dt.Rows[0]["FACTORYNAME"].ToString();
                    DESCRIPTION = dt.Rows[0]["DESCRIPTION"].ToString();
                    SPECNAME = dt.Rows[0]["SPECNAME"].ToString();
                }
                result = Repository.Electricconsumption(model, FACTORYNAME, DESCRIPTION, SPECNAME);

            }
            catch (Exception ex)
            {
                //异常处理
            }
            return result;
        }

        public ResultStatus ExternalContainerStatus(ExternalContainerStatusModel.RequestInfo model)
        {
            ResultStatus oResult = new ResultStatus();
            oResult.ResultCode = "1";

            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                DataTable dt = Repository.GetExternalContainerStatus(model.packageno);
                if (dt != null && dt.Rows.Count > 0)
                {
                    EapParameterModel.ResultContent content = new EapParameterModel.ResultContent();
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        content.packageno = dt.Rows[i]["BOXPACKSERIALNUMBER"].ToString();
                        content.efficiency = dt.Rows[i]["EFFICIENCYNAME"].ToString();

                        if (dt.Rows[i]["VOLTAGE"].ToString().Contains("高开压"))
                            content.voltage = "1";
                        else if (dt.Rows[i]["VOLTAGE"].ToString().Contains("中开压"))
                            content.voltage = "2";
                        else if (dt.Rows[i]["VOLTAGE"].ToString().Contains("低开压"))
                            content.voltage = "3";
                        else
                            content.voltage = "99";

                        content.color = GetColorValue(dt.Rows[i]["FRONTCOLORNAME"].ToString());
                        content.dcclass = dt.Rows[i]["dcclass"].ToString();
                        content.holdstatus = dt.Rows[i]["HOLDSTATUS"].ToString();

                        oResult.Content.Add(content);
                    }
                }
            }
            catch (Exception ex)
            {
                oResult.ResultCode = "0";
                throw;
            }
            return oResult;
        }


        public string GetColorValue(string color)
        {
            string colorValue = "";
            string eapColorSetting = ConfigReader.ConfigEntity.EapColorSetting;
            string[] keys = eapColorSetting.Split('|');
            foreach (var key in keys)
            {
                string[] value = key.Split('-');

                if (color.Contains(value[0]))
                {
                    colorValue = value[1];
                    break;
                }
            }
            return colorValue;
        }

        /// <summary>
        /// 获取上到工序
        /// </summary>
        /// <param name="actioncode">当前工序的actioncode</param>
        /// <returns></returns>
        public LastSpecModel GetLastSpec(string actioncode)
        {
            LastSpecModel lastmodel = new LastSpecModel();
            if (actioncode == "ZR_OUT")
            {
                lastmodel.LastActionCode = "ZR_IN";
                lastmodel.IntervalMinutes = 180;
                lastmodel.lottype = "C";
                lastmodel.lotspeccode = "01";
            }
            else if (actioncode == "PK_IN")
            {
                lastmodel.LastActionCode = "ZR_OUT";
                lastmodel.IntervalMinutes = 60;
                lastmodel.lottype = "S";
                lastmodel.lotspeccode = "02";
            }
            else if (actioncode == "PK_OUT")
            {
                lastmodel.LastActionCode = "PK_IN";
                lastmodel.IntervalMinutes = 180;
                lastmodel.lottype = "X";
                lastmodel.lotspeccode = "02";
            }
            else if (actioncode == "SE_IN")
            {
                lastmodel.LastActionCode = "PK_OUT";
                lastmodel.IntervalMinutes = 60;
                lastmodel.lottype = "S";
                lastmodel.lotspeccode = "03";
            }
            else if (actioncode == "SE_OUT")
            {
                lastmodel.LastActionCode = "SE_IN";
                lastmodel.IntervalMinutes = 5;
                lastmodel.lottype = "X";
                lastmodel.lotspeccode = "03";
            }
            else if (actioncode == "TH_IN")
            {
                lastmodel.LastActionCode = "SE_OUT";
                lastmodel.IntervalMinutes = 60;
                lastmodel.lottype = "S";
                lastmodel.lotspeccode = "04";
            }
            else if (actioncode == "TH_OUT")
            {
                lastmodel.LastActionCode = "TH_IN";
                lastmodel.IntervalMinutes = 180;
                lastmodel.lottype = "X";
                lastmodel.lotspeccode = "04";
            }
            else if (actioncode == "BSG_IN")
            {
                lastmodel.LastActionCode = "TH_OUT";
                lastmodel.IntervalMinutes = 60;
                lastmodel.lottype = "S";
                lastmodel.lotspeccode = "05";
            }
            else if (actioncode == "JP_OUT")
            {
                lastmodel.LastActionCode = "BSG_IN";
                lastmodel.IntervalMinutes = 90;
                lastmodel.lottype = "X";
                lastmodel.lotspeccode = "05";
            }
            else if (actioncode == "LP_IN")
            {
                lastmodel.LastActionCode = "JP_OUT";
                lastmodel.IntervalMinutes = 60;
                lastmodel.lottype = "S";
                lastmodel.lotspeccode = "06";
            }
            else if (actioncode == "LP_OUT")
            {
                lastmodel.LastActionCode = "LP_IN";
                lastmodel.IntervalMinutes = 180;
                lastmodel.lottype = "X";
                lastmodel.lotspeccode = "06";
            }
            else if (actioncode == "LK_IN")
            {
                lastmodel.LastActionCode = "LP_OUT";
                lastmodel.IntervalMinutes = 60;
                lastmodel.lottype = "S";
                lastmodel.lotspeccode = "07";
            }
            else if (actioncode == "LK_OUT")
            {
                lastmodel.LastActionCode = "LK_IN";
                lastmodel.IntervalMinutes = 180;
                lastmodel.lottype = "X";
                lastmodel.lotspeccode = "07";
            }
            else if (actioncode == "PSG_IN")
            {
                lastmodel.LastActionCode = "LK_OUT";
                lastmodel.IntervalMinutes = 60;
                lastmodel.lottype = "S";
                lastmodel.lotspeccode = "08";
            }
            else if (actioncode == "PSG_OUT")
            {
                lastmodel.LastActionCode = "PSG_IN";
                lastmodel.IntervalMinutes = 90;
                lastmodel.lottype = "X";
                lastmodel.lotspeccode = "08";
            }
            else if (actioncode == "ALD_IN")
            {
                lastmodel.LastActionCode = "PSG_OUT";
                lastmodel.IntervalMinutes = 60;
                lastmodel.lottype = "S";
                lastmodel.lotspeccode = "09";
            }
            else if (actioncode == "ALD_OUT")
            {
                lastmodel.LastActionCode = "ALD_IN";
                lastmodel.IntervalMinutes = 180;
                lastmodel.lottype = "S";
                lastmodel.lotspeccode = "09";
            }
            else if (actioncode == "ZM_IN")
            {
                lastmodel.LastActionCode = "ALD_OUT";
                lastmodel.IntervalMinutes = 60;
                lastmodel.lottype = "S";
                lastmodel.lotspeccode = "10";
            }
            else if (actioncode == "ZM_OUT")
            {
                lastmodel.LastActionCode = "ZM_IN";
                lastmodel.IntervalMinutes = 180;
                lastmodel.lottype = "X";
                lastmodel.lotspeccode = "10";
            }
            else if (actioncode == "BM_IN")
            {
                lastmodel.LastActionCode = "ZM_OUT";
                lastmodel.IntervalMinutes = 60;
                lastmodel.lottype = "S";
                lastmodel.lotspeccode = "11";
            }
            else if (actioncode == "BM_OUT")
            {
                lastmodel.LastActionCode = "BM_IN";
                lastmodel.IntervalMinutes = 180;
                lastmodel.lottype = "X";
                lastmodel.lotspeccode = "11";
            }
            else if (actioncode == "SW_IN")
            {
                lastmodel.LastActionCode = "BM_OUT";
                lastmodel.IntervalMinutes = 60;
                lastmodel.lottype = "S";
                lastmodel.lotspeccode = "12";
            }

            return lastmodel;
        }

        public bool InsertLot()
        {
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            bool result = false;
            DataTable wipdt = Repository.GetEAPWIPData();
            if (wipdt?.Rows.Count == 0)
            {
                return true;
            }
            DateTime begintime = DateTime.Now;
            //缓存的，待保存的LotID，
            List<EAPWipMoveHistoryModel> toSaveModels = new List<EAPWipMoveHistoryModel>();
            //从wipdt中获取待查询的
            var minTime = wipdt.AsEnumerable().Min(x => x["CREATETIME"].ToDateTime().Value.AddMinutes((int)(decimal)x["INTERVALMINUTES"])).AddMinutes(-180);
            var maxTime = wipdt.AsEnumerable().Max(x => x["CREATETIME"].ToDateTime().Value);
            var listCARRIERID = wipdt.AsEnumerable().Where(x => x["LASTACTIONCODE"].ToString().EndsWith("_OUT")).GroupBy(x => new { CARRIERID = x["CARRIERLD"].ToString(), LASTACTIONCODE = x["LASTACTIONCODE"].ToString() }, (g, r) => new EAPWipMoveHistoryModel { CARRIERID = g.CARRIERID, ACTIONCODE = g.LASTACTIONCODE, QTY = r.Count().ToString() }).ToList();
            var listEQUIPMENT = wipdt.AsEnumerable().Where(x => !x["LASTACTIONCODE"].ToString().EndsWith("_OUT")).GroupBy(x => new { EQUIPMENT = x["EQUIPMENT"].ToString(), LASTACTIONCODE = x["LASTACTIONCODE"].ToString() }, (g, r) => new EAPWipMoveHistoryModel { EQUIPMENT = g.EQUIPMENT, ACTIONCODE = g.LASTACTIONCODE, QTY = r.Count().ToString() }).ToList();

            var maxCnt = Math.Max(listCARRIERID?.Max(x => int.Parse(x.QTY)) ?? 0, listEQUIPMENT?.Max(x => int.Parse(x.QTY)) ?? 0);
            Repository.BatchGetLotID(toSaveModels, listCARRIERID, listEQUIPMENT, minTime, maxTime, maxCnt);

            //Random rd = new Random();
            for (int i = 0; i < wipdt.Rows.Count; i++)
            {
                EAPWipMoveHistoryModel wipmodel = new EAPWipMoveHistoryModel();
                wipmodel.CARRIERID = wipdt.Rows[i]["CARRIERLD"].ToString();
                wipmodel.ACTIONCODE = wipdt.Rows[i]["ACTIONCODE"].ToString();
                wipmodel.BOATID = wipdt.Rows[i]["BOATLD"].ToString();
                wipmodel.EQUIPMENT = wipdt.Rows[i]["EQUIPMENT"].ToString();
                wipmodel.TUBEID = wipdt.Rows[i]["TUBELD"].ToString();
                wipmodel.QTY = wipdt.Rows[i]["QTY"].ToString();
                wipmodel.BOXLNFO = wipdt.Rows[i]["BOXLNFO"].ToString();
                wipmodel.CREATETIME = wipdt.Rows[i]["CREATETIME"].ToDateTime();
                wipmodel.ID = Guid.NewGuid().ToString();

                wipmodel.EAPLOTIDHISTORYID = wipdt.Rows[i]["ID"].ToString();
                LastSpecModel lastmodel = new LastSpecModel
                {
                    IntervalMinutes = (decimal)wipdt.Rows[i]["INTERVALMINUTES"],
                    LastActionCode = wipdt.Rows[i]["LASTACTIONCODE"].ToString(),
                    lottype = wipdt.Rows[i]["LOTTYPE"].ToString(),
                    lotspeccode = wipdt.Rows[i]["LOTSPECCODE"].ToString(),
                }; //GetLastSpec(wipmodel.ACTIONCODE);
                   //从缓存中取最近的且status=0的一笔，如果没有则从DB中取，从DB中取的话，需要排除之前已经取出过的那几笔
                EAPWipMoveHistoryModel existEntity = GetLastOneLotID_FromCache(toSaveModels, lastmodel, wipmodel.CARRIERID, wipmodel.CREATETIME, wipmodel.EQUIPMENT);
                if (existEntity == null)
                {
                    //existEntity = Repository.GetLastOneLotID(lastmodel, wipmodel.CARRIERID, wipmodel.CREATETIME, wipmodel.EQUIPMENT, toSaveModels);
                }
                if (existEntity != null)
                {
                    wipmodel.LotID = existEntity.LotID;
                    wipmodel.LASTHISTORYID = existEntity.ID;
                    //将已存在的一笔数据修改其status=1
                    existEntity.STATUS = 1;
                    SaveLot_ToCache(toSaveModels, existEntity);
                }
                else
                {
                    int index = wipmodel.EQUIPMENT.IndexOf("-") - 2;
                    if (index < 0)
                    {
                        index = wipmodel.EQUIPMENT.Length - 2;
                    }
                    string line = wipmodel.EQUIPMENT.Substring(index, 2);
                    TimeSpan sp = DateTime.Now - new DateTime(1970, 1, 1, 0, 0, 0, 0);
                    long number = Convert.ToInt64(sp.TotalMilliseconds) + i;
                    //string str= number+ rd.Next(100, 1000).ToString();
                    string lotid = lastmodel.lottype + lastmodel.lotspeccode + line + number;

                    wipmodel.LotID = lotid;
                }
                wipmodel.STATUS = 0;
                wipmodel.__optype = OpType.A;
                //保存到缓存中
                SaveLot_ToCache(toSaveModels, wipmodel);
                //result = Repository.InsertLot(wipmodel);
            }
            Repository.BatchInsertLot(toSaveModels);

            DateTime? lastwipcreatetime = wipdt.Rows[wipdt.Rows.Count - 1]["CREATETIME"].ToDateTime();
            bool insertjobcontrol = Repository.InsertJobControl(begintime, lastwipcreatetime);
            return insertjobcontrol;
        }

        /// <summary>
        /// 从缓存中取最近的一笔新生成的且Status=0的LotID
        /// </summary>
        /// <param name="toSaveModels"></param>
        /// <param name="model"></param>
        /// <param name="CARRIERID"></param>
        /// <param name="CREATETIME"></param>
        /// <param name="EQUIPMENT"></param>
        /// <returns></returns>
        private EAPWipMoveHistoryModel GetLastOneLotID_FromCache(List<EAPWipMoveHistoryModel> toSaveModels, LastSpecModel model, string CARRIERID, DateTime? CREATETIME, string EQUIPMENT)
        {
            if (model.LastActionCode.EndsWith("_OUT"))
            {
                return toSaveModels
                .Where(x => x.ACTIONCODE == model.LastActionCode
                && x.CARRIERID == CARRIERID
                && x.STATUS == 0
                && (x.CREATETIME >= CREATETIME.Value.AddMinutes(-(int)model.IntervalMinutes) && x.CREATETIME <= CREATETIME.Value))
                .LastOrDefault();
            }
            else
            {
                return toSaveModels
                .Where(x => x.ACTIONCODE == model.LastActionCode
                && x.EQUIPMENT == EQUIPMENT
                && x.STATUS == 0
                && (x.CREATETIME >= CREATETIME.Value.AddMinutes(-(int)model.IntervalMinutes) && x.CREATETIME <= CREATETIME.Value))
                .FirstOrDefault();
            }
        }

        /// <summary>
        /// 将这笔数据保存到缓存中
        /// </summary>
        /// <param name="toSaveModels"></param>
        /// <param name="wipmodel"></param>
        /// <returns></returns>
        private void SaveLot_ToCache(List<EAPWipMoveHistoryModel> toSaveModels, EAPWipMoveHistoryModel wipmodel)
        {
            //之前保存过的
            if (toSaveModels.Any(x => x.ID == wipmodel.ID))
            {
                toSaveModels.Remove(toSaveModels.First(x => x.ID == wipmodel.ID));
            }
            toSaveModels.Add(wipmodel);
        }


        /// <summary>
        /// 石墨舟清洗数据采集(每个舟下设备时上报信息)
        /// </summary>
        /// <param name="model"></param>
        /// <param name="datacontrol"></param>
        /// <returns></returns>
        public bool SaveEqpGraphiteBoatCollect(GraphiteBoatCollectModel.RequestInfo model)
        {
            bool result = false;
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                result = Repository.SaveEqpGraphiteBoatCollect(model);
            }
            catch (Exception ex)
            {
                //异常处理
                throw;
            }
            return result;
        }

        /// <summary>
        /// 湿法工序数据采集
        /// </summary>
        /// <param name="model"></param>
        /// <param name="datacontrol"></param>
        /// <returns></returns>
        public bool SaveEnergyCollectInterface(EnergyCollectModel.RequestInfo model)
        {
            bool result = false;
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            try
            {
                result = Repository.SaveEnergyCollectInterface(model);
            }
            catch
            {
                //异常处理
                throw;
            }
            return result;
        }

        public void SaveLKTemperatureCollect(List<LKTemperatureCollectModel.EAP_LK_TEMPERATURE_COLLECT> models)
        {
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            Repository.SaveLKTemperatureCollect(models);
        }

        public void SaveTubeRunStatus(List<TubeRunStatusCheckModel.JA_TUBE_RUN_STATUS> models)
        {
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            Repository.SaveTubeRunStatus(models);
        }

        public EquipmentAPIResult GetSmallBoxLabelParam(Entities.Models.EQP.JAEQUIPMENTBOXRECORD entity)
        {
            EapInterfaceRepository Repository = new EapInterfaceRepository();
            // 1. 保存
            Repository.SaveJaEquipmentBoxRecord(entity);
            // 2. 保存到JABOXRECORD
            // 3. 获取标签模板
            LabelTemplateModel LabelTemplate = GetBoxProductLabelTemplate();
            // 4. 获取标签参数
            LabelParameterModel queryParameter = new LabelParameterModel();
            queryParameter.LabelTemplateId = LabelTemplate.LabelTemplateId;
            List<LabelParameterModel> LabelParameterList = GetBoxLabelParameterList(queryParameter);
            // 5. 获取标签参数规则
            LabelParameterRuleModel queryParameterRule = new LabelParameterRuleModel();
            queryParameterRule.LabelTemplateId = LabelTemplate.LabelTemplateId;
            List<LabelParameterRuleModel> LabelParameterRuleList = GetBoxLabelParameterRuleList(queryParameterRule);
            // 6. 获取打印所需的模板字段信息
            ///加载设备
            SortingMachineConfigModel SortingMachineConfig = GetBoxSortingMachineConfigByEquipment(entity.Equipment);
            int Hour = DateTime.Now.Hour;//当前时间点
            SortingMachineConfig.ShiftName = Hour >= 8 && Hour < 20 ? "白班" : "夜班";
            ///加载分档
            List<GradeConfigModel> GradeConfigList = GetBoxGradeConfigListByEquipment(entity.Equipment);
            GradeConfigModel GradeConfig = GradeConfigList.Where(a => a.BinNumberName == entity.Carrier).FirstOrDefault();
            SortingMachineConfig.strUserName = entity.ConsoleId;
            SortingMachineConfig.StrRemark = "";
            SortingMachineConfig.ClassType = GradeConfig.Class;
            // 获取标签参数
            Hashtable hashtable = BoxLabelParameterAssignValue(LabelParameterList, LabelParameterRuleList,
                SortingMachineConfig, GradeConfig, entity);
            hashtable.Add("GUID", entity.GUID);
            BartenderPrintModel bartenderPrint = new BartenderPrintModel();
            bartenderPrint.TemplateParameter = hashtable;//打印信息
            bartenderPrint.LabelKeySerialNumber = bartenderPrint.TemplateParameter["BoxSerialNumber"].ToString();
            // 获取特殊代码
            List<SpecialCodeModel> SpecialCodeList = GetBoxSpecialCodeAll();
            SpecialCodeModel specialCodeModel = SpecialCodeList.Where(a => a.SpecialCodeNotes == GradeConfig.SpecialCodeNotes).FirstOrDefault();
            // 获取电池匹配属性
            List<SolarCellAttributeModel> SolarCellAttributeList = GetBoxSolarCellAttributeAll();
            SolarCellAttributeModel solarCellAttributeModel = SolarCellAttributeList.Where(a => a.SolarCellAttributeName == GradeConfig.Attribute).FirstOrDefault();

            // 获取电压等级
            List<VoltageLevelModel> VoltageLevelList = GetBoxVoltageLevelAll();
            VoltageLevelModel voltageLevelModel = VoltageLevelList.Where(a => a.VoltageLevelName == GradeConfig.VoltageLevelName).FirstOrDefault();


            BoxRecordModel box = new BoxRecordModel();
            #region 打印和信息存储
            box.BoxRecordId = Guid.NewGuid().ToString("N");
            //box.Status = "正常";
            //box.BoxType = "自动打印";
            //box.PrintTime = DateTime.Now;
            box.PrintEmployee = entity.ConsoleId;
            box.PrinterName = entity.MACAdd;
            box.DcType = bartenderPrint.TemplateParameter["BoxType"] == null ? "" : bartenderPrint.TemplateParameter["BoxType"].ToString();
            box.DcClass = GradeConfig.Class;

            box.BoxSerialNumber = bartenderPrint.LabelKeySerialNumber;
            //box.ProductCode = bartenderPrint.TemplateParameter["BoxProductCode"].ToString();
            //box.ProductId = bartenderPrint.TemplateParameter["BoxProductId"].ToString();
            box.ProductCode = "ProductCode";//正泰无此字段，但需要校验，故写固定字符串
            box.ProductId = "ProductId";//正泰无此字段，但需要校验，故写固定字符串
            box.ProductName = GradeConfig.ProductName == null ? SortingMachineConfig.ProductName : GradeConfig.ProductName;
            box.ManufactureId = bartenderPrint.TemplateParameter["BoxManufactureId"].ToString();
            box.BinNumber = bartenderPrint.TemplateParameter["BoxBinNumber"].ToString();

            box.Efficiency = bartenderPrint.TemplateParameter["BoxEfficiency"].ToString();
            box.Power = bartenderPrint.TemplateParameter["BoxPower"].ToString();
            box.Color = GradeConfig.FrontColorName;
            //box.Color = bartenderPrint.TemplateParameter["BoxColor"].ToString();
            box.Quantity = int.Parse(entity.QTY);
            box.QuantityDescription = bartenderPrint.TemplateParameter["BoxQuantity"].ToString();

            box.EapBoxRecordId = entity.EAPBOXRECORDID;
            box.SortingMachineConfigId = SortingMachineConfig.SortingMachineConfigId;
            box.GradeConfigId = GradeConfig.GradeConfigId;

            box.Equipment = GradeConfig.EquipmentNotes;

            box.Factory = SortingMachineConfig.FactoryName;
            box.FrontColor = GradeConfig.FrontColorName;
            box.BackColor = GradeConfig.BackColorName;
            box.SpecialCode = specialCodeModel == null ? "" : specialCodeModel.SpecialCodeName;//特殊代码
            box.FinishedProductQuality = SortingMachineConfig.FinishedProductQualityName;
            box.SiliconWaferSource = SortingMachineConfig.SiliconWaferSourceName;
            box.PositiveGridSlurry = SortingMachineConfig.PositiveGridSlurryName;
            box.BackPoleSlurry = SortingMachineConfig.BackPoleSlurryName;
            box.BackFieldSlurry = SortingMachineConfig.BackFieldSlurryName;
            box.SolarCellAttribute = GradeConfig.Attribute;//电池匹配属性
            box.SolarCellManufacturer = SortingMachineConfig.SolarCellManufacturerName;
            box.SolarCellMachine = SortingMachineConfig.SolarCellMachineName;
            //box.TeamSchedule = hsPrintFeedback["BSN-TeamScheduleNotes"].ToString();
            box.TeamSchedule = "";
            box.Operation = entity.ConsoleId;

            //box.NonprintingManufactureId = hsPrintFeedback["BoxNonprintingManufactureId"].ToString();
            box.NonprintingManufactureId = "";

            box.Class = GradeConfig.Class;//等级

            box.ResistivityName = SortingMachineConfig.ResistivityName;

            BoxConfigModel boxConfig = new BoxConfigModel();
            boxConfig.BoxConfigId = Guid.NewGuid().ToString("N");
            boxConfig.BoxRecordId = box.BoxRecordId;
            boxConfig.EapBoxRecordId = entity.EAPBOXRECORDID;
            boxConfig.SortingMachineConfigId = SortingMachineConfig.SortingMachineConfigId;
            boxConfig.GradeConfigId = GradeConfig.GradeConfigId;
            boxConfig.MixProduct = SortingMachineConfig.MixProduct;

            //电池片车间
            boxConfig.SolarCellFactroyId = SortingMachineConfig.WorkshopId;
            boxConfig.SolarCellFactroyName = SortingMachineConfig.WorkshopName;
            boxConfig.SolarCellFactroyDescription = SortingMachineConfig.WorkshopDescription;
            boxConfig.SolarCellFactroyNotes = SortingMachineConfig.WorkshopNotes;


            boxConfig.FactoryId = SortingMachineConfig.FactoryId;
            boxConfig.FactoryName = SortingMachineConfig.FactoryName;
            boxConfig.FactoryDescription = SortingMachineConfig.FactoryDescription;
            boxConfig.FactoryNotes = SortingMachineConfig.FactoryNotes;

            boxConfig.EquipmentId = SortingMachineConfig.EquipmentId;
            boxConfig.EquipmentName = SortingMachineConfig.EquipmentName;
            boxConfig.EquipmentDescription = SortingMachineConfig.EquipmentDescription;
            boxConfig.EquipmentNotes = SortingMachineConfig.EquipmentNotes;

            boxConfig.SolarCellAttributeId = solarCellAttributeModel == null ? "" : solarCellAttributeModel.SolarCellAttributeId;
            boxConfig.SolarCellAttributeName = solarCellAttributeModel == null ? "" : solarCellAttributeModel.SolarCellAttributeName;
            boxConfig.SolarCellAttributeDescription = solarCellAttributeModel == null ? "" : solarCellAttributeModel.SolarCellAttributeDescription;
            boxConfig.SolarCellAttributeNotes = solarCellAttributeModel == null ? "" : solarCellAttributeModel.SolarCellAttributeNotes;

            boxConfig.SolarCellCompleteTypeId = SortingMachineConfig.SolarCellCompleteTypeId;
            boxConfig.SolarCellCompleteTypeName = SortingMachineConfig.SolarCellCompleteTypeName;
            boxConfig.SolarCellCompleteTypeDescription = SortingMachineConfig.SolarCellCompleteTypeDescription;
            boxConfig.SolarCellCompleteTypeNotes = SortingMachineConfig.SolarCellCompleteTypeNotes;

            boxConfig.SolarCellMachineId = SortingMachineConfig.SolarCellMachineId;
            boxConfig.SolarCellMachineName = SortingMachineConfig.SolarCellMachineName;
            boxConfig.SolarCellMachineDescription = SortingMachineConfig.SolarCellMachineDescription;
            boxConfig.SolarCellMachineNotes = SortingMachineConfig.SolarCellMachineNotes;

            boxConfig.SolarCellManufacturerId = SortingMachineConfig.SolarCellManufacturerId;
            boxConfig.SolarCellManufacturerName = SortingMachineConfig.SolarCellManufacturerName;
            boxConfig.******************************** = SortingMachineConfig.********************************;
            boxConfig.SolarCellManufacturerNotes = SortingMachineConfig.SolarCellManufacturerNotes;

            boxConfig.SpecialCodeId = specialCodeModel == null ? "" : specialCodeModel.SpecialCodeId;
            boxConfig.SpecialCodeName = specialCodeModel == null ? "" : specialCodeModel.SpecialCodeName;
            boxConfig.SpecialCodeDescription = specialCodeModel == null ? "" : specialCodeModel.SpecialCodeDescription;
            boxConfig.SpecialCodeNotes = specialCodeModel == null ? "" : specialCodeModel.SpecialCodeNotes;


            boxConfig.FullBoxQuantityId = SortingMachineConfig.FullBoxQuantityId;
            boxConfig.FullBoxQuantityName = SortingMachineConfig.FullBoxQuantityName;
            boxConfig.FullBoxQuantityDescription = SortingMachineConfig.FullBoxQuantityDescription;
            boxConfig.FullBoxQuantityNotes = SortingMachineConfig.FullBoxQuantityNotes;

            boxConfig.CustomerTechnologyTypeId = SortingMachineConfig.CustomerTechnologyTypeId;
            boxConfig.CustomerTechnologyTypeName = SortingMachineConfig.CustomerTechnologyTypeName;
            boxConfig.CustomerTechnologyTypeDescription = SortingMachineConfig.CustomerTechnologyTypeDescription;
            boxConfig.CustomerTechnologyTypeNotes = SortingMachineConfig.CustomerTechnologyTypeNotes;

            boxConfig.SolarCellTypeId = SortingMachineConfig.SolarCellTypeId;
            boxConfig.SolarCellTypeName = SortingMachineConfig.SolarCellTypeName;
            boxConfig.SolarCellTypeDescription = SortingMachineConfig.SolarCellTypeDescription;
            boxConfig.SolarCellTypeNotes = SortingMachineConfig.SolarCellTypeNotes;

            boxConfig.VoltageLevelId = voltageLevelModel == null ? "" : voltageLevelModel.VoltageLevelId;
            boxConfig.VoltageLevelName = voltageLevelModel == null ? "" : voltageLevelModel.VoltageLevelName;
            boxConfig.VoltageLevelDescription = voltageLevelModel == null ? "" : voltageLevelModel.VoltageLevelDescription;
            boxConfig.VoltageLevelNotes = voltageLevelModel == null ? "" : voltageLevelModel.VoltageLevelNotes;

            boxConfig.ProductName = GradeConfig.ProductName == null ? SortingMachineConfig.ProductName : GradeConfig.ProductName;

            boxConfig.SiliconWaferTypeId = SortingMachineConfig.SiliconWaferTypeId;
            boxConfig.SiliconWaferTypeName = SortingMachineConfig.SiliconWaferTypeName;
            boxConfig.SiliconWaferTypeDescription = SortingMachineConfig.SiliconWaferTypeDescription;
            boxConfig.SiliconWaferTypeNotes = SortingMachineConfig.SiliconWaferTypeNotes;

            boxConfig.SiliconWaferSizeId = SortingMachineConfig.SiliconWaferSizeId;
            boxConfig.SiliconWaferSizeName = SortingMachineConfig.SiliconWaferSizeName;
            boxConfig.SiliconWaferSizeDescription = SortingMachineConfig.SiliconWaferSizeDescription;
            boxConfig.SiliconWaferSizeNotes = SortingMachineConfig.SiliconWaferSizeNotes;

            boxConfig.TechnologyTypeId = SortingMachineConfig.TechnologyTypeId;
            boxConfig.TechnologyTypeName = SortingMachineConfig.TechnologyTypeName;
            boxConfig.TechnologyTypeDescription = SortingMachineConfig.TechnologyTypeDescription;
            boxConfig.TechnologyTypeNotes = SortingMachineConfig.TechnologyTypeNotes;

            boxConfig.SpecialInstructionId = SortingMachineConfig.SpecialInstructionId;
            boxConfig.SpecialInstructionName = SortingMachineConfig.SpecialInstructionName;
            boxConfig.SpecialInstructionDescription = SortingMachineConfig.SpecialInstructionDescription;
            boxConfig.SpecialInstructionNotes = SortingMachineConfig.SpecialInstructionNotes;

            boxConfig.MainGridQuantityId = SortingMachineConfig.MainGridQuantityId;
            boxConfig.MainGridQuantityName = SortingMachineConfig.MainGridQuantityName;
            boxConfig.MainGridQuantityDescription = SortingMachineConfig.MainGridQuantityDescription;
            boxConfig.MainGridQuantityNotes = SortingMachineConfig.MainGridQuantityNotes;

            boxConfig.ProductID = "ProductId";//正泰无此字段，但需要校验，故写固定字符串

            boxConfig.SiliconWaferTypeCodeId = SortingMachineConfig.SiliconWaferTypeCodeId;
            boxConfig.SiliconWaferTypeCodeName = SortingMachineConfig.SiliconWaferTypeCodeName;
            boxConfig.SiliconWaferTypeCodeDescription = SortingMachineConfig.SiliconWaferTypeCodeDescription;
            boxConfig.SiliconWaferTypeCodeNotes = SortingMachineConfig.SiliconWaferTypeCodeNotes;

            boxConfig.TechnologyEditionId = SortingMachineConfig.TechnologyEditionId;
            boxConfig.TechnologyEditionName = SortingMachineConfig.TechnologyEditionName;
            boxConfig.TechnologyEditionDescription = SortingMachineConfig.TechnologyEditionDescription;
            boxConfig.TechnologyEditionNotes = SortingMachineConfig.TechnologyEditionNotes;

            boxConfig.DopingInformationId = SortingMachineConfig.DopingInformationId;
            boxConfig.DopingInformationName = SortingMachineConfig.DopingInformationName;
            boxConfig.DopingInformationDescription = SortingMachineConfig.DopingInformationDescription;
            boxConfig.DopingInformationNotes = SortingMachineConfig.DopingInformationNotes;

            boxConfig.AdditionalCodeId = SortingMachineConfig.AdditionalCodeId;
            boxConfig.AdditionalCodeName = SortingMachineConfig.AdditionalCodeName;
            boxConfig.AdditionalCodeDescription = SortingMachineConfig.AdditionalCodeDescription;
            boxConfig.AdditionalCodeNotes = SortingMachineConfig.AdditionalCodeNotes;

            boxConfig.ProductCode = "ProductCode";//正泰无此字段，但需要校验，故写固定字符串

            boxConfig.FinishedProductQualityId = SortingMachineConfig.FinishedProductQualityId;
            boxConfig.FinishedProductQualityName = SortingMachineConfig.FinishedProductQualityName;
            boxConfig.FinishedProductQualityDescription = SortingMachineConfig.FinishedProductQualityDescription;
            boxConfig.FinishedProductQualityNotes = SortingMachineConfig.FinishedProductQualityNotes;

            boxConfig.PECVDTypeId = SortingMachineConfig.PECVDTypeId;
            boxConfig.PECVDTypeName = SortingMachineConfig.PECVDTypeName;
            boxConfig.PECVDTypeDescription = SortingMachineConfig.PECVDTypeDescription;
            boxConfig.PECVDTypeNotes = SortingMachineConfig.PECVDTypeNotes;

            boxConfig.SiliconWaferSourceId = SortingMachineConfig.SiliconWaferSourceId;
            boxConfig.SiliconWaferSourceName = SortingMachineConfig.SiliconWaferSourceName;
            boxConfig.SiliconWaferSourceDescription = SortingMachineConfig.SiliconWaferSourceDescription;
            boxConfig.SiliconWaferSourceNotes = SortingMachineConfig.SiliconWaferSourceNotes;

            boxConfig.RawMaterialQualityId = SortingMachineConfig.RawMaterialQualityId;
            boxConfig.RawMaterialQualityName = SortingMachineConfig.RawMaterialQualityName;
            boxConfig.RawMaterialQualityDescription = SortingMachineConfig.RawMaterialQualityDescription;
            boxConfig.RawMaterialQualityNotes = SortingMachineConfig.RawMaterialQualityNotes;

            boxConfig.PositiveGridSlurryId = SortingMachineConfig.PositiveGridSlurryId;
            boxConfig.PositiveGridSlurryName = SortingMachineConfig.PositiveGridSlurryName;
            boxConfig.PositiveGridSlurryDescription = SortingMachineConfig.PositiveGridSlurryDescription;
            boxConfig.PositiveGridSlurryNotes = SortingMachineConfig.PositiveGridSlurryNotes;

            boxConfig.BackPoleSlurryId = SortingMachineConfig.BackPoleSlurryId;
            boxConfig.BackPoleSlurryName = SortingMachineConfig.BackPoleSlurryName;
            boxConfig.BackPoleSlurryDescription = SortingMachineConfig.BackPoleSlurryDescription;
            boxConfig.BackPoleSlurryNotes = SortingMachineConfig.BackPoleSlurryNotes;

            boxConfig.BackFieldSlurryId = SortingMachineConfig.BackFieldSlurryId;
            boxConfig.BackFieldSlurryName = SortingMachineConfig.BackFieldSlurryName;
            boxConfig.BackFieldSlurryDescription = SortingMachineConfig.BackFieldSlurryDescription;
            boxConfig.BackFieldSlurryNotes = SortingMachineConfig.BackFieldSlurryNotes;

            boxConfig.DownshiftInformationId = SortingMachineConfig.DownshiftInformationId;
            boxConfig.DownshiftInformationName = SortingMachineConfig.DownshiftInformationName;
            boxConfig.DownshiftInformationDescription = SortingMachineConfig.DownshiftInformationDescription;
            boxConfig.DownshiftInformationNotes = SortingMachineConfig.DownshiftInformationNotes;

            boxConfig.BinNumberId = GradeConfig.BinNumberId;
            boxConfig.BinNumberName = GradeConfig.BinNumberName;
            boxConfig.BinNumberDescription = GradeConfig.BinNumberDescription;
            boxConfig.BinNumberNotes = GradeConfig.BinNumberNotes;

            boxConfig.BackColorId = GradeConfig.BackColorId;
            boxConfig.BackColorName = GradeConfig.BackColorName;
            boxConfig.BackColorDescription = GradeConfig.BackColorDescription;
            boxConfig.BackColorNotes = GradeConfig.BackColorNotes;

            boxConfig.FrontColorId = GradeConfig.FrontColorId;
            boxConfig.FrontColorName = GradeConfig.FrontColorName;
            boxConfig.FrontColorDescription = GradeConfig.FrontColorDescription;
            boxConfig.FrontColorNotes = GradeConfig.FrontColorNotes;

            boxConfig.EfficiencyId = GradeConfig.EfficiencyId;
            boxConfig.EfficiencyName = GradeConfig.EfficiencyName;
            boxConfig.EfficiencyDescription = GradeConfig.EfficiencyDescription;
            boxConfig.EfficiencyNotes = GradeConfig.EfficiencyNotes;

            boxConfig.PowerId = GradeConfig.PowerId;
            boxConfig.PowerName = GradeConfig.PowerName;
            boxConfig.PowerDescription = GradeConfig.PowerDescription;
            boxConfig.PowerNotes = GradeConfig.PowerNotes;

            boxConfig.PackagePower = GradeConfig.PackagePower;
            boxConfig.GradeRemark = GradeConfig.Remark;
            //boxConfig.TeamScheduleName = hsPrintFeedback["BSN-TeamScheduleNotes"].ToString();
            boxConfig.TeamScheduleName = "";

            boxConfig.OperationId = entity.MACAdd;
            boxConfig.OperationName = entity.ConsoleId;
            boxConfig.OperationDescription = entity.ConsoleId;
            boxConfig.OperationNotes = SortingMachineConfig.EquipmentNotes;


            boxConfig.ResistivityId = SortingMachineConfig.ResistivityId;
            boxConfig.ResistivityName = SortingMachineConfig.ResistivityName;
            boxConfig.ResistivityDescription = SortingMachineConfig.ResistivityDescription;
            boxConfig.ResistivityNotes = SortingMachineConfig.ResistivityNotes;
            #endregion

            // 1. 保存打印数据
            Repository.SaveJaBoxRecord(box, boxConfig);

            // 3. 返回标签参数
            return new EquipmentAPIResult
            {
                Result = hashtable
            };
        }

        private List<VoltageLevelModel> GetBoxVoltageLevelAll()
        {
            // 1.1 从redis上获取
            const string REDIS_KEY = "BOX_VOLTAGE_LEVEL";
            RedisClient redisClient = RedisClient.Init;
            List<VoltageLevelModel> result = redisClient.GetStringKey<List<VoltageLevelModel>>(REDIS_KEY);
            // 1.2 从数据库中获取
            if (result == null)
            {
                EapInterfaceRepository Repository = new EapInterfaceRepository();
                result = Repository.GetVoltageLevelAll();
                redisClient.SetStringKey(REDIS_KEY, result, TimeSpan.FromMinutes(11));
            }
            return result;
        }

        private List<SolarCellAttributeModel> GetBoxSolarCellAttributeAll()
        {
            // 1.1 从redis上获取
            const string REDIS_KEY = "BOX_SOLAR_CELL_ATTRIBUTE";
            RedisClient redisClient = RedisClient.Init;
            List<SolarCellAttributeModel> result = redisClient.GetStringKey<List<SolarCellAttributeModel>>(REDIS_KEY);
            // 1.2 从数据库中获取
            if (result == null)
            {
                EapInterfaceRepository Repository = new EapInterfaceRepository();
                result = Repository.GetSolarCellAttributeAll();
                redisClient.SetStringKey(REDIS_KEY, result, TimeSpan.FromMinutes(10));
            }
            return result;
        }

        private List<SpecialCodeModel> GetBoxSpecialCodeAll()
        {
            // 1.1 从redis上获取
            const string REDIS_KEY = "BOX_SPECIAL_CODE";
            RedisClient redisClient = RedisClient.Init;
            List<SpecialCodeModel> result = redisClient.GetStringKey<List<SpecialCodeModel>>(REDIS_KEY);
            // 1.2 从数据库中获取
            if (result == null)
            {
                EapInterfaceRepository Repository = new EapInterfaceRepository();
                result = Repository.GetSpecialCodeAll();
                redisClient.SetStringKey(REDIS_KEY, result, TimeSpan.FromMinutes(9));
            }
            return result;
        }

        private List<GradeConfigModel> GetBoxGradeConfigListByEquipment(string equipment)
        {
            // 1.1 从redis上获取
            const string REDIS_KEY = "BOX_GRADE_CONFIG";
            RedisClient redisClient = RedisClient.Init;
            List<GradeConfigModel> result = redisClient.HashGet<List<GradeConfigModel>>(REDIS_KEY, equipment);
            // 1.2 从数据库中获取
            if (result == null)
            {
                EapInterfaceRepository Repository = new EapInterfaceRepository();
                GradeConfigModel queryGradeConfig = new GradeConfigModel();
                queryGradeConfig.EquipmentName = equipment;
                result = Repository.GetGradeConfigListtByEquipment(queryGradeConfig);
                redisClient.HashSet(REDIS_KEY, new Dictionary<string, List<GradeConfigModel>>
                {
                    { equipment, result }
                }, TimeSpan.FromMinutes(8));
            }
            return result;
        }

        private SortingMachineConfigModel GetBoxSortingMachineConfigByEquipment(string equipment)
        {
            // 1.1 从redis上获取
            const string REDIS_KEY = "BOX_SORTING_MACHINE_CONFIG";
            RedisClient redisClient = RedisClient.Init;
            SortingMachineConfigModel result = redisClient.HashGet<SortingMachineConfigModel>(REDIS_KEY, equipment);
            // 1.2 从数据库中获取
            if (result == null)
            {
                EapInterfaceRepository Repository = new EapInterfaceRepository();
                SortingMachineConfigModel querySortingMachineConfig = new SortingMachineConfigModel();
                querySortingMachineConfig.EquipmentName = equipment;
                result = Repository.GetSortingMachineConfigByEquipment(querySortingMachineConfig);
                redisClient.HashSet(REDIS_KEY, new Dictionary<string, SortingMachineConfigModel>
                {
                    { equipment, result }
                }, TimeSpan.FromMinutes(10));
            }
            return result;
        }

        private List<LabelParameterRuleModel> GetBoxLabelParameterRuleList(LabelParameterRuleModel queryParameterRule)
        {
            // 1.1 从redis上获取
            const string REDIS_KEY = "BOX_LABEL_PARAMETER_RULE";
            RedisClient redisClient = RedisClient.Init;
            List<LabelParameterRuleModel> result = redisClient.GetStringKey<List<LabelParameterRuleModel>>(REDIS_KEY);
            // 1.2 从数据库中获取
            if (result == null)
            {
                EapInterfaceRepository Repository = new EapInterfaceRepository();
                result = Repository.GetLabelParameterRuleList(queryParameterRule);
                redisClient.SetStringKey(REDIS_KEY, result, TimeSpan.FromDays(1));
            }
            return result;
        }

        private List<LabelParameterModel> GetBoxLabelParameterList(LabelParameterModel queryParameter)
        {
            // 1.1 从redis上获取
            const string REDIS_KEY = "BOX_LABEL_PARAMETER";
            RedisClient redisClient = RedisClient.Init;
            List<LabelParameterModel> result = redisClient.GetStringKey<List<LabelParameterModel>>(REDIS_KEY);
            // 1.2 从数据库中获取
            if (result == null)
            {
                EapInterfaceRepository Repository = new EapInterfaceRepository();
                result = Repository.GetLabelParameterList(queryParameter);
                redisClient.SetStringKey(REDIS_KEY, result, TimeSpan.FromDays(1));
            }
            return result;
        }

        private LabelTemplateModel GetBoxProductLabelTemplate()
        {
            // 1.1 从redis上获取
            const string REDIS_KEY = "BOX_PRODUCT_LABEL_TEMPLATE";
            RedisClient redisClient = RedisClient.Init;
            LabelTemplateModel result = redisClient.GetStringKey<LabelTemplateModel>(REDIS_KEY);
            // 1.2 从数据库中获取
            if (result == null)
            {
                EapInterfaceRepository Repository = new EapInterfaceRepository();
                LabelTemplateModel query = new LabelTemplateModel();
                query.LabelTemplateTypeName = "BoxTemplate";
                result = Repository.GetProductLabelTemplate(query);
                redisClient.SetStringKey(REDIS_KEY, result, TimeSpan.FromDays(1));
            }
            return result;
        }

        private Hashtable BoxLabelParameterAssignValue(List<LabelParameterModel> lstLabelParameter, List<LabelParameterRuleModel> lstLabelParameterRuleAll,
            SortingMachineConfigModel sortingMachineConfig, GradeConfigModel gradeConfig, JAEQUIPMENTBOXRECORD eapBox
           )

        {
            Hashtable htTemplateParameter = new Hashtable();
            try
            {
                LabelParameterModel parameterKeySerialNumber = lstLabelParameter.Find(p => p.LabelParameterNotes == "KeySerialNumber");
                if (parameterKeySerialNumber == null)
                {
                    //int idx = lstLabelParameter.IndexOf(parameterKeySerialNumber);
                    //lstLabelParameter.RemoveAt(idx);
                    //lstLabelParameter.Insert(0, "KeySerialNumber");
                }

                foreach (var parameter in lstLabelParameter)
                {
                    switch (parameter.LabelParameterTypeName)
                    {
                        case "SerialNumber":
                            string strSerialNumber = "";
                            List<LabelParameterRuleModel> lstLabelParameterRule = lstLabelParameterRuleAll.FindAll(r => r.LabelParameterId == parameter.LabelParameterId).ToList();
                            //获取序号的逻辑需要变更
                            //strSerialNumber = LabelSerialNumberHelper.CombineBoxSerialNumber(lstLabelParameterRule, sortingMachineConfig, gradeConfig, serialNumberSequence, printEmployee, ref hsPrintFeedback);
                            string stProductName = gradeConfig.ProductName;
                            string stBin = gradeConfig.BinNumberName;
                            if (string.IsNullOrEmpty(stProductName))
                                throw new Exception("PN Name不能为空");
                            if (stProductName.Length < 5)
                                throw new Exception("PN Name不能小于5位");
                            if (string.IsNullOrWhiteSpace(stBin))
                                throw new Exception("Bin号不能为空");
                            string strFCode = sortingMachineConfig.WorkshopDescription;
                            if (string.IsNullOrEmpty(strFCode))
                                throw new Exception("工厂代码不能为空");
                            string strEquipment = sortingMachineConfig.EquipmentName;
                            if (string.IsNullOrEmpty(strEquipment))
                                throw new Exception("线体不能为空");
                            string dcPrefixString = stProductName.Substring(stProductName.Length - 5, 5) + strFCode;
                            // 三位BIN号
                            dcPrefixString += stBin.PadLeft(3, '0');
                            // 线体2位
                            dcPrefixString += strEquipment.Substring(strEquipment.Length - 2, 2);
                            // 六位日期码
                            dcPrefixString += DateTime.Now.ToString("yyMMdd");

                            MIService s = new MIService();
                            ResultStatus rs = s.GetPackageSN(ConfigReader.CamstarUserName, ConfigReader.CamstarUserPwd, dcPrefixString, dcPrefixString, "BoxNumber");

                            if (rs.ResultCode == "0")
                                throw new Exception(rs.ResultMsg);
                            else
                                strSerialNumber = rs.ResultFilename;
                            //strSerialNumber = dcPrefixString + new Random().Next(1, 9999).ToString("0000");//暂时写死
                            if (parameter.NonprintingParameter)
                            {
                                // hsPrintFeedback.Add(parameter.LabelParameterName, strSerialNumber);
                            }
                            else
                            {
                                htTemplateParameter.Add(parameter.LabelParameterName, strSerialNumber);
                            }
                            break;
                        case "Type":
                            string strType = sortingMachineConfig.SolarCellCompleteTypeName + "-" + sortingMachineConfig.SolarCellTypeName + sortingMachineConfig.MainGridQuantityName + "-" + sortingMachineConfig.TechnologyTypeName;
                            htTemplateParameter.Add(parameter.LabelParameterName, strType);
                            break;
                        case "Remark":
                            htTemplateParameter.Add(parameter.LabelParameterName, sortingMachineConfig.StrRemark);
                            break;
                        case "Desc":
                            if (eapBox != null)
                            {
                                sortingMachineConfig.InPutPrintQty = eapBox.QTY;
                            }
                            string strDesc = sortingMachineConfig.SolarCellTypeName + "-" + sortingMachineConfig.MainGridQuantityNotes + "-" + sortingMachineConfig.SiliconWaferMainGridTypeName + "(" + sortingMachineConfig.InPutPrintQty + ")";//满包数量InPutPrintQty
                            htTemplateParameter.Add(parameter.LabelParameterName, strDesc);
                            break;
                        case "P/N":
                            string strPN = gradeConfig.ProductName;
                            htTemplateParameter.Add(parameter.LabelParameterName, strPN);
                            break;
                        case "VoltageLevel":
                            string strBoxVoltageLevel = gradeConfig.VoltageLevelName;
                            htTemplateParameter.Add(parameter.LabelParameterName, strBoxVoltageLevel);
                            break;
                        case "Shift":
                            string strShift = sortingMachineConfig.ShiftName;
                            htTemplateParameter.Add(parameter.LabelParameterName, strShift);
                            break;
                        case "BoxEquipment":
                            string strBoxEquipment = sortingMachineConfig.EquipmentNotes;
                            htTemplateParameter.Add(parameter.LabelParameterName, strBoxEquipment);
                            break;
                        case "BoxSolarCellManufacturer":
                            string strBoxSolarCellManufacturer = sortingMachineConfig.SolarCellManufacturerName;
                            htTemplateParameter.Add(parameter.LabelParameterName, strBoxSolarCellManufacturer);
                            break;
                        case "ProductId":
                            string strProductId = sortingMachineConfig.ProductID;
                            htTemplateParameter.Add(parameter.LabelParameterName, strProductId);
                            break;
                        case "ProductName":
                            string strProductName = gradeConfig.ProductName;
                            htTemplateParameter.Add(parameter.LabelParameterName, strProductName);
                            break;
                        case "ProductCode":
                            string strProductCode = sortingMachineConfig.ProductCode;
                            htTemplateParameter.Add(parameter.LabelParameterName, strProductCode);
                            break;
                        case "ManufactureId":
                            string strManufactureId = sortingMachineConfig.SolarCellManufacturerName;
                            //string strManufactureId = "";
                            htTemplateParameter.Add(parameter.LabelParameterName, strManufactureId);
                            break;
                        case "Efficiency":
                            string strEfficiency = gradeConfig.EfficiencyName;
                            htTemplateParameter.Add(parameter.LabelParameterName, strEfficiency);
                            break;
                        case "BinNumber":
                            string strBoxBin = gradeConfig.BinNumberName;
                            htTemplateParameter.Add(parameter.LabelParameterName, strBoxBin);
                            break;
                        case "Printer":
                            //string strPacker = printEmployee.EmployeeName;
                            htTemplateParameter.Add(parameter.LabelParameterName, sortingMachineConfig.strUserName);
                            //if (printEmployee.EmployeeName.Length >=2)
                            //{
                            //    string strTeamScheduleName = printEmployee.EmployeeName.Substring(1, 1);
                            //    hsPrintFeedback.Add("TeamScheduleName", strTeamScheduleName);
                            //}
                            //TeamScheduleModel query = new TeamScheduleModel();
                            //query.TeamScheduleName = strTeamScheduleName;
                            //TeamScheduleModel teamSchedule = service.GetTeamScheduleByName(query);
                            //if (teamSchedule != null)
                            //{
                            //    strTeamSchedule = teamSchedule.TeamScheduleName;
                            //}
                            break;
                        case "Power":
                            string strPower = gradeConfig.PowerName + gradeConfig.VoltageLevelNotes;
                            htTemplateParameter.Add(parameter.LabelParameterName, strPower);
                            break;
                        case "Color":
                            string strColor = gradeConfig.FrontColorName + "/" + gradeConfig.Class;
                            htTemplateParameter.Add(parameter.LabelParameterName, strColor);
                            break;
                        case "Class":
                            string strClass = gradeConfig.Class;
                            htTemplateParameter.Add(parameter.LabelParameterName, strClass);
                            break;
                        case "Line":
                            string strLine = gradeConfig.EquipmentNotes;
                            htTemplateParameter.Add(parameter.LabelParameterName, strLine);
                            break;
                        case "BoxQuantity":
                            string strBoxQuantity = "";
                            if (eapBox != null)
                            {
                                strBoxQuantity = eapBox.QTY;
                            }
                            else
                            {
                                strBoxQuantity = sortingMachineConfig.FullBoxQuantityName + sortingMachineConfig.FullBoxQuantityNotes;
                            }
                            htTemplateParameter.Add(parameter.LabelParameterName, strBoxQuantity);
                            break;
                        case "Date":
                            if (!string.IsNullOrEmpty(parameter.ParameterFormat))
                            {
                                htTemplateParameter.Add(parameter.LabelParameterName, DateTime.Now.ToString(parameter.ParameterFormat));
                            }
                            else
                            {
                                htTemplateParameter.Add(parameter.LabelParameterName, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return htTemplateParameter;
        }

        public EquipmentAPIResult SaveSmallBoxLabelPrintStatus(APISmallBoxLabelPrintStatus entity)
        {
            try
            {
                EapInterfaceRepository Repository = new EapInterfaceRepository();
                Repository.SaveSmallBoxLabelPrintStatus(new BoxRecordModel
                {
                    BoxSerialNumber = entity.BoxSerialNumber,
                    Status = entity.Status,
                    BoxType = "自动打印",
                    PrintTime = DateTime.TryParse(entity.PrintTime, out DateTime dateTime) ? dateTime : DateTime.Now
                });
                return new EquipmentAPIResult()
                {
                    Success = true,
                    Message = "保存成功"
                };
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// 检查并调整参数值，针对ZR、JP、RCA工序，从Redis获取正确的参数值
        /// </summary>
        /// <param name="model">设备参数模型</param>
        /// <param name="saveParams">过滤后的参数列表</param>
        /// <returns>调整后的设备参数模型</returns>
        private EqpParamModel checkAndAdjustParamValues(EqpParamModel model, List<EqpParamModel.EqpParamListItem> saveParams)
        {
            // 仅当工序为ZR、JP、RCA或ZMDM时执行参数调整
            if (!new string[] { "ZR", "JP", "RCA", "ZMDM" }.Contains(model.ActionCode))
                return model;

            try
            {
                RedisClient redisClient = RedisClient.Init;
                string redisKey = $"EqpParam:{model.ActionCode}:{model.Equipment}";

                Dictionary<string, int> diAdjustParam = new Dictionary<string, int>()
                {
                    { "DI", 200000 },//DI补加
                    { "CH2", 5000 },//H2O2补加
                    { "CH1", 1000 },//NaOH补加
                    { "CH4", 200 }//ADD补加
                };

                void AdjustParamValue(EqpParamModel.EqpParamListItem param)
                {
                    double paramValue;
                    if (!double.TryParse(param.value, out paramValue))
                        return;

                    string redisValue = string.Empty;

                    string paramName = param.name.Substring(param.name.LastIndexOf("_") + 1);

                    if (param.name.StartsWith("实时补液量_", StringComparison.OrdinalIgnoreCase) &&
                        diAdjustParam.ContainsKey(paramName))
                    {
                        if (paramValue > diAdjustParam[paramName])
                        {
                            redisValue = redisClient.HashGet<string>(redisKey, param.name);
                            if (!string.IsNullOrEmpty(redisValue))
                            {
                                param.value = redisValue;
                            }
                        }
                        else
                        {
                            redisClient.HashSet(redisKey, new Dictionary<string, string>
                            {
                                { param.name, paramValue.ToString() }
                            });
                        }
                    }
                }

                foreach (var param in saveParams)
                {
                    // 跳过名称为空的参数
                    if (string.IsNullOrEmpty(param.name))
                        continue;

                    // 处理ZMDM工序的RFPV1和RFPV2参数
                    if (model.ActionCode == "ZMDM")
                    {
                        if (param.name == "RFPV1" || param.name == "RFPV2")
                        {
                            // 当参数值为0或空时，从Redis获取值
                            if (string.IsNullOrEmpty(param.value) || param.value == "0")
                            {
                                string redisValue = redisClient.HashGet<string>(redisKey, param.name);
                                if (!string.IsNullOrEmpty(redisValue))
                                {
                                    param.value = redisValue;
                                }
                            }
                            else
                            {
                                // 否则，将参数值保存到Redis
                                redisClient.HashSet(redisKey, new Dictionary<string, string>
                                {
                                    { param.name, param.value }
                                });
                            }
                        }
                    }
                    else if (new string[] { "ZR", "JP", "RCA" }.Contains(model.ActionCode))
                    {
                        AdjustParamValue(param);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error($"检查调整参数值失败：{ex.Message}", ex);
            }

            return model;
        }
    }
}
